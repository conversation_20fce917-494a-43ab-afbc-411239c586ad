/**
 * DataTable Component
 * Reusable table component with pagination, sorting, filtering, and actions
 */

import React, { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { ChevronUp, ChevronDown, Search, Filter, MoreHorizontal, Eye, Edit, Trash2 } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card';
import { Button } from '../../../components/ui/Button';
import { Input } from '../../../components/ui/Input';
import { Badge } from '../../../components/ui/badge';
import { cn } from '../../../lib/utils';
import type { ColumnDefinition, TableAction, PaginationConfig } from '../../types/common';

interface DataTableProps<T = any> {
  data: T[];
  columns: ColumnDefinition<T>[];
  loading?: boolean;
  error?: string | null;
  pagination?: PaginationConfig;
  actions?: TableAction<T>[];
  searchable?: boolean;
  searchPlaceholder?: string;
  filterable?: boolean;
  selectable?: boolean;
  selectedRows?: T[];
  onRowSelect?: (rows: T[]) => void;
  onRowClick?: (row: T) => void;
  onSort?: (field: string, direction: 'asc' | 'desc') => void;
  onPageChange?: (page: number) => void;
  onPageSizeChange?: (pageSize: number) => void;
  onSearch?: (query: string) => void;
  className?: string;
  variant?: 'default' | 'glass' | 'minimal';
  size?: 'sm' | 'md' | 'lg';
  emptyMessage?: string;
  emptyIcon?: React.ComponentType<{ className?: string }>;
}

const DataTable = <T extends Record<string, any>>({
  data,
  columns,
  loading = false,
  error = null,
  pagination,
  actions = [],
  searchable = true,
  searchPlaceholder,
  filterable = false,
  selectable = false,
  selectedRows = [],
  onRowSelect,
  onRowClick,
  onSort,
  onPageChange,
  onPageSizeChange,
  onSearch,
  className,
  variant = 'glass',
  size = 'md',
  emptyMessage,
  emptyIcon: EmptyIcon = Search,
}: DataTableProps<T>) => {
  const { t } = useTranslation();
  const [searchQuery, setSearchQuery] = useState('');
  const [sortField, setSortField] = useState<string | null>(null);
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [selectedRowIds, setSelectedRowIds] = useState<Set<string>>(new Set());

  // Use translation defaults if not provided
  const finalSearchPlaceholder = searchPlaceholder || t('ui.searchPlaceholder');
  const finalEmptyMessage = emptyMessage || t('messages.noData');

  // Handle search
  const handleSearch = (query: string) => {
    setSearchQuery(query);
    onSearch?.(query);
  };

  // Handle sorting
  const handleSort = (field: string) => {
    const newDirection = sortField === field && sortDirection === 'asc' ? 'desc' : 'asc';
    setSortField(field);
    setSortDirection(newDirection);
    onSort?.(field, newDirection);
  };

  // Handle row selection
  const handleRowSelect = (row: T, checked: boolean) => {
    const rowId = row.id?.toString() || '';
    const newSelectedIds = new Set(selectedRowIds);
    
    if (checked) {
      newSelectedIds.add(rowId);
    } else {
      newSelectedIds.delete(rowId);
    }
    
    setSelectedRowIds(newSelectedIds);
    const selectedData = data.filter(item => newSelectedIds.has(item.id?.toString() || ''));
    onRowSelect?.(selectedData);
  };

  // Handle select all
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      const allIds = new Set(data.map(item => item.id?.toString() || ''));
      setSelectedRowIds(allIds);
      onRowSelect?.(data);
    } else {
      setSelectedRowIds(new Set());
      onRowSelect?.([]);
    }
  };

  // Get variant classes
  const getVariantClasses = () => {
    switch (variant) {
      case 'minimal':
        return 'bg-transparent border-0 shadow-none';
      case 'default':
        return 'bg-background border border-border shadow-sm';
      default:
        return 'glass border-0 shadow-lg';
    }
  };

  // Get size classes
  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return {
          table: 'text-sm',
          padding: 'p-2',
          headerPadding: 'px-3 py-2',
          cellPadding: 'px-3 py-2',
        };
      case 'lg':
        return {
          table: 'text-base',
          padding: 'p-8',
          headerPadding: 'px-6 py-4',
          cellPadding: 'px-6 py-4',
        };
      default:
        return {
          table: 'text-sm',
          padding: 'p-6',
          headerPadding: 'px-4 py-3',
          cellPadding: 'px-4 py-3',
        };
    }
  };

  const sizeClasses = getSizeClasses();
  const isAllSelected = data.length > 0 && selectedRowIds.size === data.length;
  const isIndeterminate = selectedRowIds.size > 0 && selectedRowIds.size < data.length;

  if (loading) {
    return (
      <Card className={cn(getVariantClasses(), className)}>
        <CardContent className={sizeClasses.padding}>
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4"></div>
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-12 bg-gray-200 dark:bg-gray-700 rounded"></div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={cn(getVariantClasses(), className)}>
        <CardContent className={sizeClasses.padding}>
          <div className="text-center py-8">
            <p className="text-red-600 dark:text-red-400 mb-4">{error}</p>
            <Button variant="outline" onClick={() => window.location.reload()}>
              {t('common.tryAgain')}
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn(getVariantClasses(), className)}>
      {/* Header with search and filters */}
      {(searchable || filterable) && (
        <CardHeader>
          <div className="flex items-center justify-between gap-4">
            {searchable && (
              <div className="relative flex-1 max-w-sm">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  type="text"
                  placeholder={finalSearchPlaceholder}
                  value={searchQuery}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-10"
                  variant="glass"
                />
              </div>
            )}
            
            {filterable && (
              <Button variant="outline" size="sm">
                <Filter className="w-4 h-4 mr-2" />
                {t('common.filter')}
              </Button>
            )}
          </div>
        </CardHeader>
      )}

      <CardContent className={cn(sizeClasses.padding, 'pt-0')}>
        {data.length === 0 ? (
          <div className="text-center py-12">
            <EmptyIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500 dark:text-gray-400">{finalEmptyMessage}</p>
          </div>
        ) : (
          <>
            {/* Table */}
            <div className="overflow-x-auto">
              <table className={cn('w-full', sizeClasses.table)}>
                <thead>
                  <tr className="border-b border-gray-200 dark:border-gray-700">
                    {selectable && (
                      <th className={sizeClasses.headerPadding}>
                        <input
                          type="checkbox"
                          checked={isAllSelected}
                          ref={(el) => {
                            if (el) el.indeterminate = isIndeterminate;
                          }}
                          onChange={(e) => handleSelectAll(e.target.checked)}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                      </th>
                    )}
                    
                    {columns.map((column) => (
                      <th
                        key={column.key as string}
                        className={cn(
                          sizeClasses.headerPadding,
                          'text-left font-medium macos-text-secondary',
                          column.sortable && 'cursor-pointer hover:macos-text-primary',
                          column.align === 'center' && 'text-center',
                          column.align === 'right' && 'text-right'
                        )}
                        style={{ width: column.width }}
                        onClick={() => column.sortable && handleSort(column.key as string)}
                      >
                        <div className="flex items-center gap-2">
                          {column.title}
                          {column.sortable && (
                            <div className="flex flex-col">
                              <ChevronUp 
                                className={cn(
                                  'w-3 h-3',
                                  sortField === column.key && sortDirection === 'asc' 
                                    ? 'text-blue-600' 
                                    : 'text-gray-400'
                                )} 
                              />
                              <ChevronDown 
                                className={cn(
                                  'w-3 h-3 -mt-1',
                                  sortField === column.key && sortDirection === 'desc' 
                                    ? 'text-blue-600' 
                                    : 'text-gray-400'
                                )} 
                              />
                            </div>
                          )}
                        </div>
                      </th>
                    ))}
                    
                    {actions.length > 0 && (
                      <th className={cn(sizeClasses.headerPadding, 'text-right')}>
                        {t('common.actions')}
                      </th>
                    )}
                  </tr>
                </thead>
                
                <tbody>
                  {data.map((row, index) => {
                    const rowId = row.id?.toString() || '';
                    const isSelected = selectedRowIds.has(rowId);
                    
                    return (
                      <tr
                        key={rowId || index}
                        className={cn(
                          'border-b border-gray-100 dark:border-gray-800 hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors',
                          onRowClick && 'cursor-pointer',
                          isSelected && 'bg-blue-50 dark:bg-blue-900/20'
                        )}
                        onClick={() => onRowClick?.(row)}
                      >
                        {selectable && (
                          <td className={sizeClasses.cellPadding}>
                            <input
                              type="checkbox"
                              checked={isSelected}
                              onChange={(e) => {
                                e.stopPropagation();
                                handleRowSelect(row, e.target.checked);
                              }}
                              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                            />
                          </td>
                        )}
                        
                        {columns.map((column) => (
                          <td
                            key={column.key as string}
                            className={cn(
                              sizeClasses.cellPadding,
                              'macos-text-primary',
                              column.align === 'center' && 'text-center',
                              column.align === 'right' && 'text-right',
                              column.className
                            )}
                          >
                            {column.render 
                              ? column.render(row[column.key as keyof T], row, index)
                              : row[column.key as keyof T]
                            }
                          </td>
                        ))}
                        
                        {actions.length > 0 && (
                          <td className={cn(sizeClasses.cellPadding, 'text-right')}>
                            <div className="flex items-center justify-end gap-2">
                              {actions.map((action) => {
                                const Icon = action.icon || MoreHorizontal;
                                const isDisabled = action.disabled?.(row) || false;
                                
                                return (
                                  <Button
                                    key={action.key}
                                    variant="ghost"
                                    size="sm"
                                    disabled={isDisabled}
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      if (action.confirm) {
                                        if (window.confirm(`${action.confirm.title}\n\n${action.confirm.message}`)) {
                                          action.onClick(row);
                                        }
                                      } else {
                                        action.onClick(row);
                                      }
                                    }}
                                    className={cn(
                                      action.variant === 'danger' && 'text-red-600 hover:text-red-800',
                                      action.variant === 'primary' && 'text-blue-600 hover:text-blue-800'
                                    )}
                                  >
                                    <Icon className="w-4 h-4" />
                                  </Button>
                                );
                              })}
                            </div>
                          </td>
                        )}
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            {pagination && (
              <div className="flex items-center justify-between mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
                <div className="text-sm macos-text-secondary">
                  {t('ui.showingResults')} {((pagination.page - 1) * pagination.pageSize) + 1} {t('ui.to')}{' '}
                  {Math.min(pagination.page * pagination.pageSize, pagination.total)} {t('ui.of')}{' '}
                  {pagination.total} {t('ui.results')}
                </div>
                
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    disabled={pagination.page <= 1}
                    onClick={() => onPageChange?.(pagination.page - 1)}
                  >
                    {t('common.previous')}
                  </Button>

                  <span className="text-sm macos-text-secondary">
                    {t('ui.page')} {pagination.page} {t('ui.pageOf')} {Math.ceil(pagination.total / pagination.pageSize)}
                  </span>

                  <Button
                    variant="outline"
                    size="sm"
                    disabled={pagination.page >= Math.ceil(pagination.total / pagination.pageSize)}
                    onClick={() => onPageChange?.(pagination.page + 1)}
                  >
                    {t('common.next')}
                  </Button>
                </div>
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
};

export default DataTable;

// Preset table variants
export const GlassDataTable = <T extends Record<string, any>>(props: DataTableProps<T>) => (
  <DataTable {...props} variant="glass" />
);

export const MinimalDataTable = <T extends Record<string, any>>(props: DataTableProps<T>) => (
  <DataTable {...props} variant="minimal" />
);

// Common table actions - now using translation keys
export const commonTableActions = {
  view: (onClick: (row: any) => void): TableAction => ({
    key: 'view',
    label: 'View', // Will be translated in component
    icon: Eye,
    onClick,
    variant: 'primary',
  }),

  edit: (onClick: (row: any) => void): TableAction => ({
    key: 'edit',
    label: 'Edit', // Will be translated in component
    icon: Edit,
    onClick,
    variant: 'primary',
  }),

  delete: (onClick: (row: any) => void): TableAction => ({
    key: 'delete',
    label: 'Delete', // Will be translated in component
    icon: Trash2,
    onClick,
    variant: 'danger',
    confirm: {
      title: 'Confirm Delete', // Will be translated in component
      message: 'Are you sure you want to delete this item? This action cannot be undone.', // Will be translated in component
    },
  }),
};
