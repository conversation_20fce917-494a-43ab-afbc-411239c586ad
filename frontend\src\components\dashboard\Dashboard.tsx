import React, { useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import type { RootState, AppDispatch } from '../../store';
import { logout } from '../../store/slices/authSlice';
import { Button } from '../ui/Button';
import LanguageSwitcher from '../ui/LanguageSwitcher';
import { useTheme } from '../../hooks/useTheme';

import RoleDashboard from './RoleDashboard';
import { getSidebarNavClass, getAvatarClass, getButtonClass } from '../../utils/styleUtils';
import AnalyticsDashboard from './AnalyticsDashboard';
import PatientList from '../patients/PatientList';
import PatientRegistrationForm from '../patients/PatientRegistrationForm';
import AppointmentCalendar from '../appointments/AppointmentCalendar';
import EmergencyDashboard from '../emergency/EmergencyDashboard';
import EmergencyManagement from '../emergency/EmergencyManagement';
import CommunicationsManagement from '../communications/CommunicationsManagement';
import StaffManagement from '../staff/StaffManagement';
import InventoryManagement from '../inventory/InventoryManagement';
import MedicalRecords from '../medical/MedicalRecords';
import PrescriptionManagement from '../medical/PrescriptionManagement';
import LabTestManagement from '../medical/LabTestManagement';
import UserManagement from '../admin/UserManagement';
import BillingManagement from '../billing/BillingManagement';
import StyleDemo from '../demo/StyleDemo';
import StyleGuide from '../demo/StyleGuide';


import MyMedicalRecords from '../patients/MyMedicalHistory';
import MyPrescriptions from '../patients/MyPrescriptions';
import MyLabResults from '../patients/MyLabResults';
import AppointmentBooking from '../patient/AppointmentBooking';
import MyAppointments from '../patients/MyAppointments';
import PatientCare from '../nurses/PatientCare';
import MedicationAdministration from '../nurses/MedicationAdministration';
import MyPatients from '../doctors/MyPatients';
import MedicalNotes from '../doctors/MedicalNotes';
import MySchedule from '../doctors/MySchedule';
import ReportsAnalytics from '../admin/ReportsAnalytics';
import Settings from '../settings/Settings';
import Profile from '../profile/Profile';
import AIServices from '../../pages/AIServices';

import FloatingAIChat from '../ai/FloatingAIChat';

const Dashboard: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const navigate = useNavigate();
  const { user } = useSelector((state: RootState) => state.auth);
  const { glassmorphismLevel } = useTheme();
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState('dashboard');

  const handleLogout = async () => {
    await dispatch(logout());
    navigate('/login');
  };

  const getNavigationItems = () => {
    const baseItems = [
      { id: 'dashboard', label: t('navigation.dashboard'), icon: '📊' },
    ];

    switch (user?.role) {
      case 'admin':
        return [
          ...baseItems,
          { id: 'ai-services', label: 'AI Services', icon: '🤖' },
          { id: 'user-management', label: 'User Management', icon: '👤' },
          { id: 'patients', label: t('navigation.patients'), icon: '👥' },
          { id: 'appointments', label: t('navigation.appointments'), icon: '📅' },
          { id: 'medical-records', label: 'Medical Records', icon: '📋' },
          { id: 'emergency', label: t('navigation.emergency'), icon: '🚨' },
          { id: 'staff', label: t('navigation.staff'), icon: '👨‍⚕️' },
          { id: 'inventory', label: t('navigation.inventory'), icon: '📦' },
          { id: 'billing', label: t('navigation.billing'), icon: '💰' },
          { id: 'reports', label: t('navigation.reports'), icon: '📈' },
          { id: 'style-demo', label: 'Style Demo', icon: '🎨' },
          { id: 'style-guide', label: 'Style Guide', icon: '📋' },
        ];
      case 'doctor':
        return [
          ...baseItems,
          { id: 'ai-services', label: 'AI Services', icon: '🤖' },
          { id: 'appointments', label: t('navigation.appointments'), icon: '📅' },
          { id: 'my-patients', label: 'My Patients', icon: '👥' },
          { id: 'medical-records', label: 'Medical Records', icon: '📋' },
          { id: 'prescriptions', label: 'Prescriptions', icon: '💊' },
          { id: 'lab-tests', label: 'Lab Tests', icon: '🧪' },
          { id: 'medical-notes', label: 'Medical Notes', icon: '📝' },
          { id: 'my-schedule', label: 'My Schedule', icon: '📅' },
          { id: 'emergency', label: t('navigation.emergency'), icon: '🚨' },
        ];
      case 'nurse':
        return [
          ...baseItems,
          { id: 'patient-care', label: 'Patient Care', icon: '👥' },
          { id: 'emergency', label: 'Emergency', icon: '🚨' },
          { id: 'medications', label: 'Medications', icon: '💊' },
          { id: 'my-schedule', label: 'My Schedule', icon: '📅' },
        ];
      case 'patient':
        return [
          ...baseItems,
          { id: 'my-appointments', label: 'My Appointments', icon: '📅' },
          { id: 'my-medical-records', label: 'Medical History', icon: '📋' },
          { id: 'my-prescriptions', label: 'My Prescriptions', icon: '💊' },
          { id: 'my-lab-results', label: 'Lab Results', icon: '🧪' },
          { id: 'appointment-booking', label: 'Book Appointment', icon: '➕' },
        ];
      case 'receptionist':
        return [
          ...baseItems,
          { id: 'appointments', label: 'Appointments', icon: '📅' },
          { id: 'patients', label: 'Patients', icon: '👥' },
          { id: 'register-patient', label: 'Register Patient', icon: '➕' },
          { id: 'emergency', label: 'Emergency', icon: '🚨' },
          { id: 'billing', label: 'Billing', icon: '💰' },
        ];
      default:
        return baseItems;
    }
  };

  const renderContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return <RoleDashboard />;
      case 'ai-services':
        return <AIServices />;
      case 'analytics':
        return <AnalyticsDashboard />;
      case 'patients':
        return <PatientList />;
      case 'register-patient':
        return <PatientRegistrationForm />;
      case 'appointments':
        return <AppointmentCalendar />;
      case 'emergency':
        return <EmergencyDashboard />;
      case 'emergency-management':
        return <EmergencyManagement />;
      case 'communications':
        return <CommunicationsManagement />;
      case 'staff':
        return <StaffManagement />;
      case 'inventory':
        return <InventoryManagement />;
      case 'medical-records':
        return <MedicalRecords />;
      case 'prescriptions':
        return <PrescriptionManagement />;
      case 'lab-tests':
        return <LabTestManagement />;
      case 'user-management':
        return <UserManagement />;
      case 'billing':
        return <BillingManagement />;
      case 'style-demo':
        return <StyleDemo />;
      case 'style-guide':
        return <StyleGuide />;

      case 'my-medical-records':
        return <MyMedicalRecords />;
      case 'my-prescriptions':
        return <MyPrescriptions />;
      case 'my-lab-results':
        return <MyLabResults />;
      case 'my-appointments':
        return <MyAppointments />;
      case 'appointment-booking':
        return <AppointmentBooking />;
      case 'my-patients':
        return <MyPatients />;
      case 'medical-notes':
        return <MedicalNotes />;
      case 'my-schedule':
        return <MySchedule />;
      case 'patient-care':
        return <PatientCare />;
      case 'medications':
        return <MedicationAdministration />;
      case 'reports':
        return <ReportsAnalytics />;
      case 'settings':
        return <Settings />;
      case 'profile':
        return <Profile />;
      default:
        return <div className="text-center macos-text-secondary py-8">{t('ui.featureComingSoon')}</div>;
    }
  };

  const navigationItems = getNavigationItems();

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-indigo-900 flex">
      {/* Enhanced Main Sidebar */}
      <div
        id="main-dashboard-sidebar"
        className="w-72 sidebar-glass border-r border-gray-200/50 dark:border-gray-700/50 shadow-2xl relative flex-shrink-0 flex flex-col"
      >
        {/* Header */}
        <div className="sidebar-header">
          <div className="flex items-center gap-3 mb-4">
            <div className="sidebar-icon">
              <span className="text-white font-bold text-lg">🏥</span>
            </div>
            <h1 className="sidebar-title">
              {t('dashboard.title')}
            </h1>
          </div>
          <div className="sidebar-user-card">
            <p className="text-sm font-semibold text-gray-900 dark:text-white">{user?.full_name}</p>
            <p className="text-xs text-blue-600 dark:text-blue-400 capitalize font-medium">{user?.role}</p>
          </div>
        </div>

        {/* Navigation */}
        <nav className="sidebar-nav">
          <div className="space-y-2">
            {navigationItems.map((item, index) => (
              <button
                key={item.id}
                onClick={() => setActiveTab(item.id)}
                className={`sidebar-nav-item group ${
                  activeTab === item.id ? 'sidebar-nav-item--active' : ''
                }`}
                style={{ animationDelay: `${index * 50}ms` }}
              >
                {activeTab === item.id && (
                  <div className="sidebar-nav-indicator"></div>
                )}
                <span className="sidebar-nav-icon">
                  {item.icon}
                </span>
                <span className="sidebar-nav-label">
                  {item.label}
                </span>
              </button>
            ))}
          </div>
        </nav>

        {/* Bottom Section */}
        <div className="sidebar-bottom">
          <button
            onClick={() => setActiveTab('profile')}
            className={`sidebar-profile-btn group ${activeTab === 'profile' ? 'sidebar-profile-btn--active' : ''}`}
          >
            <div className="sidebar-avatar">
              <span className="text-sm font-bold">
                {user?.first_name?.charAt(0)}{user?.last_name?.charAt(0)}
              </span>
            </div>
            <div className="flex-1 min-w-0 rtl:text-right">
              <p className="text-sm font-semibold truncate">{user?.first_name} {user?.last_name}</p>
              <p className="text-xs opacity-75 capitalize truncate font-medium">{user?.role}</p>
            </div>
          </button>

          <button
            onClick={() => setActiveTab('settings')}
            className={`sidebar-settings-btn group ${activeTab === 'settings' ? 'sidebar-settings-btn--active' : ''}`}
          >
            <span className="sidebar-settings-icon">⚙️</span>
            <span className="rtl:text-right font-medium">{t('common.settings')}</span>
          </button>

          <button
            onClick={handleLogout}
            className="sidebar-logout-btn group"
          >
            <span className="sidebar-logout-icon">🚪</span>
            <span className="rtl:text-right font-medium">{t('auth.logout')}</span>
          </button>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col min-h-screen overflow-hidden">
        {/* Page Content */}
        <main className="flex-1 p-6 overflow-auto">
          <div className="max-w-7xl mx-auto">
            {renderContent()}
          </div>
        </main>
      </div>

      {/* Floating AI Chat */}
      <FloatingAIChat />
    </div>
  );
};

export default Dashboard;
