/**
 * Style Converter Utilities
 * Provides consistent styling functions for the HMS application
 */

// Status color mappings
export const getStatusColorClass = (status: string): string => {
  const statusMap: Record<string, string> = {
    'active': 'text-green-600 dark:text-green-400',
    'inactive': 'text-gray-500 dark:text-gray-400',
    'pending': 'text-yellow-600 dark:text-yellow-400',
    'completed': 'text-green-600 dark:text-green-400',
    'cancelled': 'text-red-600 dark:text-red-400',
    'scheduled': 'text-blue-600 dark:text-blue-400',
    'confirmed': 'text-green-600 dark:text-green-400',
    'rejected': 'text-red-600 dark:text-red-400',
    'approved': 'text-green-600 dark:text-green-400',
    'draft': 'text-gray-600 dark:text-gray-400',
    'published': 'text-blue-600 dark:text-blue-400',
    'archived': 'text-gray-500 dark:text-gray-400',
    'urgent': 'text-red-600 dark:text-red-400',
    'high': 'text-orange-600 dark:text-orange-400',
    'medium': 'text-yellow-600 dark:text-yellow-400',
    'low': 'text-green-600 dark:text-green-400',
    'critical': 'text-red-700 dark:text-red-300',
    'warning': 'text-yellow-600 dark:text-yellow-400',
    'info': 'text-blue-600 dark:text-blue-400',
    'success': 'text-green-600 dark:text-green-400',
    'error': 'text-red-600 dark:text-red-400',
  };

  return statusMap[status.toLowerCase()] || 'text-gray-600 dark:text-gray-400';
};

// Accent background classes
export const getAccentBackgroundClass = (variant: string = 'default'): string => {
  const variantMap: Record<string, string> = {
    'default': 'bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-950/20 dark:to-emerald-950/20',
    'primary': 'bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20',
    'secondary': 'bg-gradient-to-br from-gray-50 to-slate-50 dark:from-gray-950/20 dark:to-slate-950/20',
    'success': 'bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-950/20 dark:to-emerald-950/20',
    'warning': 'bg-gradient-to-br from-yellow-50 to-orange-50 dark:from-yellow-950/20 dark:to-orange-950/20',
    'error': 'bg-gradient-to-br from-red-50 to-rose-50 dark:from-red-950/20 dark:to-rose-950/20',
    'info': 'bg-gradient-to-br from-blue-50 to-cyan-50 dark:from-blue-950/20 dark:to-cyan-950/20',
    'muted': 'bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900/20 dark:to-gray-800/20',
  };

  return variantMap[variant] || variantMap.default;
};

// Text classes for consistent typography
export const getTextClasses = () => {
  return {
    heading: {
      h1: 'text-4xl font-bold tracking-tight text-foreground',
      h2: 'text-3xl font-semibold tracking-tight text-foreground',
      h3: 'text-2xl font-semibold tracking-tight text-foreground',
      h4: 'text-xl font-semibold tracking-tight text-foreground',
      h5: 'text-lg font-semibold tracking-tight text-foreground',
      h6: 'text-base font-semibold tracking-tight text-foreground',
    },
    body: {
      large: 'text-lg text-foreground',
      default: 'text-base text-foreground',
      small: 'text-sm text-foreground',
      xs: 'text-xs text-foreground',
    },
    muted: {
      large: 'text-lg text-muted-foreground',
      default: 'text-base text-muted-foreground',
      small: 'text-sm text-muted-foreground',
      xs: 'text-xs text-muted-foreground',
    },
    accent: {
      primary: 'text-primary',
      secondary: 'text-secondary',
      success: 'text-green-600 dark:text-green-400',
      warning: 'text-yellow-600 dark:text-yellow-400',
      error: 'text-red-600 dark:text-red-400',
      info: 'text-blue-600 dark:text-blue-400',
    },
  };
};

// Glass morphism effect classes
export const getGlassClasses = (intensity: 'light' | 'medium' | 'heavy' = 'medium'): string => {
  const intensityMap: Record<string, string> = {
    light: 'backdrop-blur-sm bg-white/10 dark:bg-black/10 border border-white/20 dark:border-white/10',
    medium: 'backdrop-blur-md bg-white/20 dark:bg-black/20 border border-white/30 dark:border-white/20',
    heavy: 'backdrop-blur-lg bg-white/30 dark:bg-black/30 border border-white/40 dark:border-white/30',
  };

  return intensityMap[intensity];
};

// Card variant classes
export const getCardClasses = (variant: 'default' | 'glass' | 'elevated' = 'default'): string => {
  const variantMap: Record<string, string> = {
    default: 'bg-card text-card-foreground border border-border shadow-sm',
    glass: `${getGlassClasses('medium')} text-foreground shadow-lg`,
    elevated: 'bg-card text-card-foreground border border-border shadow-lg hover:shadow-xl transition-shadow',
  };

  return variantMap[variant];
};

// Button variant classes
export const getButtonClasses = (variant: 'default' | 'primary' | 'secondary' | 'ghost' | 'glass' = 'default'): string => {
  const variantMap: Record<string, string> = {
    default: 'bg-primary text-primary-foreground hover:bg-primary/90',
    primary: 'bg-gradient-to-r from-green-600 to-emerald-600 text-white hover:from-green-700 hover:to-emerald-700',
    secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
    ghost: 'hover:bg-accent hover:text-accent-foreground',
    glass: `${getGlassClasses('light')} text-foreground hover:bg-white/30 dark:hover:bg-black/30`,
  };

  return variantMap[variant];
};

// Utility function to combine classes
export const cn = (...classes: (string | undefined | null | false)[]): string => {
  return classes.filter(Boolean).join(' ');
};

// Priority level styling using unified color system
export const getPriorityClasses = (priority: 'low' | 'medium' | 'high' | 'urgent' | 'critical'): string => {
  const priorityMap: Record<string, string> = {
    low: 'bg-emerald-100/80 text-emerald-800 dark:bg-emerald-900/20 dark:text-emerald-400 border border-emerald-200/50',
    medium: 'bg-yellow-100/80 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400 border border-yellow-200/50',
    high: 'bg-orange-100/80 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400 border border-orange-200/50',
    urgent: 'bg-red-100/80 text-red-800 dark:bg-red-900/20 dark:text-red-400 border border-red-200/50',
    critical: 'bg-red-200/80 text-red-900 dark:bg-red-900/40 dark:text-red-300 border border-red-300/50',
  };

  return priorityMap[priority] || priorityMap.medium;
};

// Role-based styling using unified color system
export const getRoleClasses = (role: 'admin' | 'doctor' | 'nurse' | 'patient' | 'staff'): string => {
  const roleMap: Record<string, string> = {
    admin: 'bg-purple-100/80 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400 border border-purple-200/50 dark:border-purple-700/50',
    doctor: 'bg-blue-100/80 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400 border border-blue-200/50 dark:border-blue-700/50',
    nurse: 'bg-emerald-100/80 text-emerald-800 dark:bg-emerald-900/20 dark:text-emerald-400 border border-emerald-200/50 dark:border-emerald-700/50',
    patient: 'bg-gray-100/80 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400 border border-gray-200/50 dark:border-gray-700/50',
    staff: 'bg-indigo-100/80 text-indigo-800 dark:bg-indigo-900/20 dark:text-indigo-400 border border-indigo-200/50 dark:border-indigo-700/50',
  };

  return roleMap[role] || roleMap.staff;
};

// Animation classes
export const getAnimationClasses = () => {
  return {
    fadeIn: 'animate-in fade-in duration-200',
    fadeOut: 'animate-out fade-out duration-200',
    slideIn: 'animate-in slide-in-from-bottom-4 duration-300',
    slideOut: 'animate-out slide-out-to-bottom-4 duration-300',
    scaleIn: 'animate-in zoom-in-95 duration-200',
    scaleOut: 'animate-out zoom-out-95 duration-200',
    pulse: 'animate-pulse',
    spin: 'animate-spin',
    bounce: 'animate-bounce',
  };
};

export default {
  getStatusColorClass,
  getAccentBackgroundClass,
  getTextClasses,
  getGlassClasses,
  getCardClasses,
  getButtonClasses,
  cn,
  getPriorityClasses,
  getRoleClasses,
  getAnimationClasses,
};
