WARNING 2025-05-26 10:58:47,372 ai_core 15952 4544 Gemini AI dependencies not installed
WARNING 2025-05-26 10:58:47,374 ai_core 15952 4544 LangGraph dependencies not installed
WARNING 2025-05-26 10:58:47,374 ai_core 15952 4544 ML dependencies not installed
WARNING 2025-05-26 10:58:47,375 ai_core 15952 4544 Gemini AI dependencies not available
WARNING 2025-05-26 10:58:47,375 ai_core 15952 4544 Gemini AI dependencies not available
WARNING 2025-05-26 10:58:56,163 ai_core 19964 18204 Gemini AI dependencies not installed
WARNING 2025-05-26 10:58:56,164 ai_core 19964 18204 LangGraph dependencies not installed
WARNING 2025-05-26 10:58:56,165 ai_core 19964 18204 ML dependencies not installed
WARNING 2025-05-26 10:58:56,165 ai_core 19964 18204 Gemini AI dependencies not available
WARNING 2025-05-26 10:58:56,166 ai_core 19964 18204 Gemini AI dependencies not available
WARNING 2025-05-26 11:06:05,589 ai_core 12460 7352 Gemini AI dependencies not installed
WARNING 2025-05-26 11:06:05,592 ai_core 12460 7352 LangGraph dependencies not installed
WARNING 2025-05-26 11:06:05,593 ai_core 12460 7352 ML dependencies not installed
WARNING 2025-05-26 11:06:05,593 ai_core 12460 7352 Gemini AI dependencies not available
WARNING 2025-05-26 11:06:05,593 ai_core 12460 7352 Gemini AI dependencies not available
WARNING 2025-05-26 11:08:10,394 log 18404 1592 Bad Request: /api/auth/login/
WARNING 2025-05-26 11:08:10,394 basehttp 18404 1592 "POST /api/auth/login/ HTTP/1.1" 400 40
WARNING 2025-05-26 11:08:45,862 log 18404 18284 Unauthorized: /api/auth/login/
WARNING 2025-05-26 11:08:45,863 basehttp 18404 18284 "POST /api/auth/login/ HTTP/1.1" 401 31
INFO 2025-05-26 11:09:23,833 basehttp 18404 17604 "POST /api/auth/login/ HTTP/1.1" 200 804
INFO 2025-05-26 11:09:51,104 basehttp 18404 8624 "POST /api/auth/login/ HTTP/1.1" 200 804
INFO 2025-05-26 11:09:53,144 basehttp 18404 4092 "GET /api/ai/health/status/ HTTP/1.1" 200 170
ERROR 2025-05-26 11:09:55,986 ai_core 18404 22136 Gemini AI error: Invalid argument provided to Gemini: 400 API key expired. Please renew the API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key expired. Please renew the API key."
]
ERROR 2025-05-26 11:09:55,987 log 18404 22136 Internal Server Error: /api/ai/conversations/chat/
ERROR 2025-05-26 11:09:55,988 basehttp 18404 22136 "POST /api/ai/conversations/chat/ HTTP/1.1" 500 332
WARNING 2025-05-26 11:50:29,631 ai_core 22520 23512 Gemini AI dependencies not installed
WARNING 2025-05-26 11:50:29,633 ai_core 22520 23512 LangGraph dependencies not installed
WARNING 2025-05-26 11:50:29,634 ai_core 22520 23512 ML dependencies not installed
WARNING 2025-05-26 11:50:29,635 ai_core 22520 23512 Gemini AI dependencies not available
WARNING 2025-05-26 11:50:29,636 ai_core 22520 23512 Gemini AI dependencies not available
WARNING 2025-05-26 11:50:47,300 ai_core 11852 22892 Gemini AI dependencies not installed
WARNING 2025-05-26 11:50:47,301 ai_core 11852 22892 LangGraph dependencies not installed
WARNING 2025-05-26 11:50:47,302 ai_core 11852 22892 ML dependencies not installed
WARNING 2025-05-26 11:50:47,302 ai_core 11852 22892 Gemini AI dependencies not available
WARNING 2025-05-26 11:50:47,303 ai_core 11852 22892 Gemini AI dependencies not available
INFO 2025-05-26 11:52:44,526 basehttp 18404 16428 "POST /api/auth/login/ HTTP/1.1" 200 804
WARNING 2025-05-26 11:52:46,773 log 18404 10748 Not Found: /api/ai/multi-agent/cases/agent_status/
WARNING 2025-05-26 11:52:46,774 basehttp 18404 10748 "GET /api/ai/multi-agent/cases/agent_status/ HTTP/1.1" 404 19975
WARNING 2025-05-26 11:52:48,838 log 18404 21304 Not Found: /api/ai/multi-agent/cases/process_case/
WARNING 2025-05-26 11:52:48,839 basehttp 18404 21304 "POST /api/ai/multi-agent/cases/process_case/ HTTP/1.1" 404 19976
WARNING 2025-05-26 11:52:50,910 log 18404 11852 Not Found: /api/ai/multi-agent/cases/emergency_activation/
WARNING 2025-05-26 11:52:50,911 basehttp 18404 11852 "POST /api/ai/multi-agent/cases/emergency_activation/ HTTP/1.1" 404 20000
WARNING 2025-05-26 11:52:52,984 log 18404 13616 Not Found: /api/ai/multi-agent/performance/system_metrics/
WARNING 2025-05-26 11:52:52,986 basehttp 18404 13616 "GET /api/ai/multi-agent/performance/system_metrics/ HTTP/1.1" 404 19999
WARNING 2025-05-26 11:52:55,057 log 18404 10740 Not Found: /api/ai/multi-agent/workflows/available_workflows/
WARNING 2025-05-26 11:52:55,058 basehttp 18404 10740 "GET /api/ai/multi-agent/workflows/available_workflows/ HTTP/1.1" 404 20008
INFO 2025-05-26 11:55:02,755 basehttp 18404 12888 "POST /api/auth/login/ HTTP/1.1" 200 804
WARNING 2025-05-26 11:55:04,801 log 18404 16776 Not Found: /api/ai/multi-agent/cases/agent_status/
WARNING 2025-05-26 11:55:04,802 basehttp 18404 16776 "GET /api/ai/multi-agent/cases/agent_status/ HTTP/1.1" 404 19975
WARNING 2025-05-26 11:55:06,841 log 18404 14248 Not Found: /api/ai/multi-agent/cases/process_case/
WARNING 2025-05-26 11:55:06,841 basehttp 18404 14248 "POST /api/ai/multi-agent/cases/process_case/ HTTP/1.1" 404 19976
WARNING 2025-05-26 11:55:08,891 log 18404 18560 Not Found: /api/ai/multi-agent/cases/emergency_activation/
WARNING 2025-05-26 11:55:08,891 basehttp 18404 18560 "POST /api/ai/multi-agent/cases/emergency_activation/ HTTP/1.1" 404 20000
WARNING 2025-05-26 11:55:10,943 log 18404 9252 Not Found: /api/ai/multi-agent/performance/system_metrics/
WARNING 2025-05-26 11:55:10,945 basehttp 18404 9252 "GET /api/ai/multi-agent/performance/system_metrics/ HTTP/1.1" 404 19999
WARNING 2025-05-26 11:55:12,998 log 18404 14624 Not Found: /api/ai/multi-agent/workflows/available_workflows/
WARNING 2025-05-26 11:55:12,999 basehttp 18404 14624 "GET /api/ai/multi-agent/workflows/available_workflows/ HTTP/1.1" 404 20008
WARNING 2025-05-26 12:02:03,967 ai_core 22456 23516 Gemini AI dependencies not installed
WARNING 2025-05-26 12:02:03,968 ai_core 22456 23516 LangGraph dependencies not installed
WARNING 2025-05-26 12:02:03,970 ai_core 22456 23516 ML dependencies not installed
WARNING 2025-05-26 12:02:03,970 ai_core 22456 23516 Gemini AI dependencies not available
WARNING 2025-05-26 12:02:03,971 ai_core 22456 23516 Gemini AI dependencies not available
INFO 2025-05-26 12:05:43,633 basehttp 16960 8608 "POST /api/auth/login/ HTTP/1.1" 200 804
INFO 2025-05-26 12:05:45,723 basehttp 16960 16916 "GET /api/ai/multi-agent/cases/agent_status/ HTTP/1.1" 200 2285
WARNING 2025-05-26 12:05:47,792 log 16960 15484 Not Found: /api/ai/multi-agent/cases/process_case/
WARNING 2025-05-26 12:05:47,792 basehttp 16960 15484 "POST /api/ai/multi-agent/cases/process_case/ HTTP/1.1" 404 45
WARNING 2025-05-26 12:05:49,845 log 16960 6872 Not Found: /api/ai/multi-agent/cases/emergency_activation/
WARNING 2025-05-26 12:05:49,846 basehttp 16960 6872 "POST /api/ai/multi-agent/cases/emergency_activation/ HTTP/1.1" 404 45
INFO 2025-05-26 12:05:51,912 basehttp 16960 21944 "GET /api/ai/multi-agent/performance/system_metrics/ HTTP/1.1" 200 212
INFO 2025-05-26 12:05:54,016 basehttp 16960 8976 "GET /api/ai/multi-agent/workflows/available_workflows/ HTTP/1.1" 200 4384
INFO 2025-05-26 12:07:48,307 basehttp 16960 15052 "POST /api/auth/login/ HTTP/1.1" 200 804
INFO 2025-05-26 12:07:50,413 basehttp 16960 20820 "GET /api/ai/multi-agent/cases/agent_status/ HTTP/1.1" 200 2285
ERROR 2025-05-26 12:07:53,825 multi_agent_system 16960 12120 AI response error for Triage Specialist: Invalid argument provided to Gemini: 400 API key expired. Please renew the API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key expired. Please renew the API key."
]
ERROR 2025-05-26 12:07:54,361 multi_agent_system 16960 12120 AI response error for Diagnostic Specialist: Invalid argument provided to Gemini: 400 API key expired. Please renew the API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key expired. Please renew the API key."
]
ERROR 2025-05-26 12:07:54,869 multi_agent_system 16960 12120 AI response error for Treatment Specialist: Invalid argument provided to Gemini: 400 API key expired. Please renew the API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key expired. Please renew the API key."
]
ERROR 2025-05-26 12:07:55,340 multi_agent_system 16960 12120 AI response error for Clinical Pharmacist: Invalid argument provided to Gemini: 400 API key expired. Please renew the API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key expired. Please renew the API key."
]
ERROR 2025-05-26 12:07:55,893 multi_agent_system 16960 12120 AI response error for Medical Coordinator: Invalid argument provided to Gemini: 400 API key expired. Please renew the API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key expired. Please renew the API key."
]
INFO 2025-05-26 12:07:56,015 basehttp 16960 12120 "POST /api/ai/multi-agent/cases/process_case/ HTTP/1.1" 200 5390
ERROR 2025-05-26 12:07:58,060 log 16960 15640 Internal Server Error: /api/ai/multi-agent/cases/771d8960-61da-4aa6-9854-2f5e1b066b15/case_details/
ERROR 2025-05-26 12:07:58,061 basehttp 16960 15640 "GET /api/ai/multi-agent/cases/771d8960-61da-4aa6-9854-2f5e1b066b15/case_details/ HTTP/1.1" 500 12
ERROR 2025-05-26 12:08:03,377 multi_agent_system 16960 19128 AI response error for Emergency Medicine Specialist: Invalid argument provided to Gemini: 400 API key expired. Please renew the API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key expired. Please renew the API key."
]
INFO 2025-05-26 12:08:03,476 basehttp 16960 19128 "POST /api/ai/multi-agent/cases/emergency_activation/ HTTP/1.1" 200 1363
INFO 2025-05-26 12:08:05,635 basehttp 16960 6128 "GET /api/ai/multi-agent/performance/system_metrics/ HTTP/1.1" 200 212
INFO 2025-05-26 12:08:07,706 basehttp 16960 5920 "GET /api/ai/multi-agent/workflows/available_workflows/ HTTP/1.1" 200 4384
INFO 2025-06-06 04:33:00,280 autoreload 6956 8020 Watching for file changes with StatReloader
INFO 2025-06-06 04:33:37,700 basehttp 6956 13132 "OPTIONS /api/auth/login/ HTTP/1.1" 200 0
INFO 2025-06-06 04:34:22,433 autoreload 6956 8020 C:\Users\<USER>\OneDrive\Desktop\HMS\backend\users\admin.py changed, reloading.
INFO 2025-06-06 04:34:26,680 autoreload 15892 14400 Watching for file changes with StatReloader
INFO 2025-06-06 04:37:10,633 basehttp 15892 20384 "GET /admin/ HTTP/1.1" 302 0
INFO 2025-06-06 04:37:10,947 basehttp 15892 20384 "GET /admin/login/?next=/admin/ HTTP/1.1" 200 4181
INFO 2025-06-06 04:37:11,044 basehttp 15892 20384 "GET /static/admin/css/base.css HTTP/1.1" 200 21310
INFO 2025-06-06 04:37:11,048 basehttp 15892 12000 "GET /static/admin/css/dark_mode.css HTTP/1.1" 200 2929
INFO 2025-06-06 04:37:11,055 basehttp 15892 11752 "GET /static/admin/css/login.css HTTP/1.1" 200 958
INFO 2025-06-06 04:37:11,063 basehttp 15892 15972 "GET /static/admin/css/responsive.css HTTP/1.1" 200 18559
INFO 2025-06-06 04:37:11,065 basehttp 15892 9068 "GET /static/admin/css/nav_sidebar.css HTTP/1.1" 200 2694
INFO 2025-06-06 04:37:12,813 basehttp 15892 9068 "GET /static/admin/js/theme.js HTTP/1.1" 200 1943
INFO 2025-06-06 04:37:12,813 basehttp 15892 15972 "GET /static/admin/js/nav_sidebar.js HTTP/1.1" 200 3063
INFO 2025-06-06 04:37:51,450 basehttp 15892 9068 "GET /api/docs/ HTTP/1.1" 200 4663
INFO 2025-06-06 04:37:54,155 basehttp 15892 9068 "GET /api/schema/ HTTP/1.1" 200 284924
INFO 2025-06-06 04:38:57,124 basehttp 15892 6840 "OPTIONS /api/auth/login/ HTTP/1.1" 200 0
INFO 2025-06-06 04:39:03,171 basehttp 15892 6840 "OPTIONS /api/auth/login/ HTTP/1.1" 200 0
INFO 2025-06-06 04:39:49,194 basehttp 15892 6840 "OPTIONS /api/auth/login/ HTTP/1.1" 200 0
INFO 2025-06-06 04:41:49,644 autoreload 15892 14400 C:\Users\<USER>\OneDrive\Desktop\HMS\backend\authentication\views.py changed, reloading.
INFO 2025-06-06 04:41:53,596 autoreload 5696 11100 Watching for file changes with StatReloader
INFO 2025-06-06 04:42:07,331 basehttp 5696 3540 "OPTIONS /api/auth/login/ HTTP/1.1" 200 0
INFO 2025-06-06 04:42:13,051 autoreload 5696 11100 C:\Users\<USER>\OneDrive\Desktop\HMS\backend\authentication\views.py changed, reloading.
INFO 2025-06-06 04:42:16,830 autoreload 5440 11204 Watching for file changes with StatReloader
INFO 2025-06-06 04:42:33,204 autoreload 5440 11204 C:\Users\<USER>\OneDrive\Desktop\HMS\backend\authentication\views.py changed, reloading.
INFO 2025-06-06 04:42:37,267 autoreload 8832 15592 Watching for file changes with StatReloader
INFO 2025-06-06 04:42:45,233 basehttp 8832 18420 "OPTIONS /api/auth/login/ HTTP/1.1" 200 0
INFO 2025-06-06 04:43:03,011 basehttp 8832 18420 "OPTIONS /api/auth/login/ HTTP/1.1" 200 0
INFO 2025-06-06 04:43:03,473 autoreload 8832 15592 C:\Users\<USER>\OneDrive\Desktop\HMS\backend\ai_services\multi_agent_views.py changed, reloading.
INFO 2025-06-06 04:43:07,475 autoreload 11716 6920 Watching for file changes with StatReloader
INFO 2025-06-06 04:43:23,990 autoreload 11716 6920 C:\Users\<USER>\OneDrive\Desktop\HMS\backend\ai_services\multi_agent_views.py changed, reloading.
INFO 2025-06-06 04:43:27,624 autoreload 12344 16212 Watching for file changes with StatReloader
INFO 2025-06-06 04:44:05,985 autoreload 12344 16212 C:\Users\<USER>\OneDrive\Desktop\HMS\backend\ai_services\views.py changed, reloading.
INFO 2025-06-06 04:44:09,640 autoreload 19896 18784 Watching for file changes with StatReloader
INFO 2025-06-06 04:44:22,907 autoreload 19896 18784 C:\Users\<USER>\OneDrive\Desktop\HMS\backend\ai_services\views.py changed, reloading.
INFO 2025-06-06 04:44:26,458 autoreload 12824 10076 Watching for file changes with StatReloader
INFO 2025-06-06 04:44:36,028 autoreload 12824 10076 C:\Users\<USER>\OneDrive\Desktop\HMS\backend\ai_services\views.py changed, reloading.
INFO 2025-06-06 04:44:39,533 autoreload 15776 18240 Watching for file changes with StatReloader
INFO 2025-06-06 04:45:07,373 autoreload 15776 18240 C:\Users\<USER>\OneDrive\Desktop\HMS\backend\ai_services\views.py changed, reloading.
INFO 2025-06-06 04:45:11,199 autoreload 7584 12000 Watching for file changes with StatReloader
INFO 2025-06-06 04:45:22,102 autoreload 7584 12000 C:\Users\<USER>\OneDrive\Desktop\HMS\backend\ai_services\views.py changed, reloading.
INFO 2025-06-06 04:45:25,869 autoreload 11084 19260 Watching for file changes with StatReloader
INFO 2025-06-06 04:45:35,426 autoreload 11084 19260 C:\Users\<USER>\OneDrive\Desktop\HMS\backend\ai_services\multi_agent_views.py changed, reloading.
INFO 2025-06-06 04:45:38,969 autoreload 17224 7904 Watching for file changes with StatReloader
INFO 2025-06-06 04:48:22,575 basehttp 17224 736 "OPTIONS /api/auth/login/ HTTP/1.1" 200 0
INFO 2025-06-06 04:48:36,397 basehttp 17224 736 "OPTIONS /api/ HTTP/1.1" 200 0
INFO 2025-06-06 04:48:36,402 basehttp 17224 20396 "OPTIONS /api/ HTTP/1.1" 200 0
INFO 2025-06-06 04:48:36,403 basehttp 17224 736 "OPTIONS /api/auth/ HTTP/1.1" 200 0
INFO 2025-06-06 04:48:36,405 basehttp 17224 5164 "OPTIONS /api/ai/health/status/ HTTP/1.1" 200 0
INFO 2025-06-06 04:48:36,407 basehttp 17224 8328 "OPTIONS /api/auth/ HTTP/1.1" 200 0
INFO 2025-06-06 04:48:36,409 basehttp 17224 768 "OPTIONS /api/ai/health/status/ HTTP/1.1" 200 0
INFO 2025-06-06 04:48:59,888 autoreload 17224 7904 C:\Users\<USER>\OneDrive\Desktop\HMS\backend\hms\settings.py changed, reloading.
INFO 2025-06-06 04:49:04,112 autoreload 17928 7336 Watching for file changes with StatReloader
INFO 2025-06-06 04:49:48,595 basehttp 17928 16552 "OPTIONS /api/auth/login/ HTTP/1.1" 200 0
WARNING 2025-06-06 04:49:49,224 log 17928 16552 Unauthorized: /api/auth/login/
WARNING 2025-06-06 04:49:49,226 basehttp 17928 16552 "POST /api/auth/login/ HTTP/1.1" 401 31
INFO 2025-06-06 04:49:53,387 autoreload 17928 7336 C:\Users\<USER>\OneDrive\Desktop\HMS\backend\hms\settings.py changed, reloading.
INFO 2025-06-06 04:49:57,884 autoreload 19476 9596 Watching for file changes with StatReloader
INFO 2025-06-06 04:50:07,549 basehttp 19476 12488 "POST /api/auth/login/ HTTP/1.1" 200 834
INFO 2025-06-06 04:50:16,975 basehttp 19476 15884 "OPTIONS /api/ HTTP/1.1" 200 0
INFO 2025-06-06 04:50:16,978 basehttp 19476 13000 "OPTIONS /api/auth/ HTTP/1.1" 200 0
INFO 2025-06-06 04:50:16,981 basehttp 19476 10584 "OPTIONS /api/ai/health/status/ HTTP/1.1" 200 0
WARNING 2025-06-06 04:50:17,001 log 19476 10584 Unauthorized: /api/ai/health/status/
WARNING 2025-06-06 04:50:17,001 basehttp 19476 10584 "GET /api/ai/health/status/ HTTP/1.1" 401 58
WARNING 2025-06-06 04:50:17,327 log 19476 13000 Not Found: /api/auth/
WARNING 2025-06-06 04:50:17,329 log 19476 15884 Not Found: /api/
WARNING 2025-06-06 04:50:17,330 basehttp 19476 13000 "OPTIONS /api/auth/ HTTP/1.1" 404 3611
WARNING 2025-06-06 04:50:17,331 basehttp 19476 15884 "GET /api/ HTTP/1.1" 404 2793
INFO 2025-06-06 04:54:36,027 basehttp 19476 8268 "OPTIONS /api/ai/health/status/ HTTP/1.1" 200 0
WARNING 2025-06-06 04:54:36,034 log 19476 8268 Unauthorized: /api/ai/health/status/
INFO 2025-06-06 04:54:36,036 basehttp 19476 5008 "OPTIONS /api/auth/ HTTP/1.1" 200 0
WARNING 2025-06-06 04:54:36,041 basehttp 19476 8268 "GET /api/ai/health/status/ HTTP/1.1" 401 58
INFO 2025-06-06 04:54:36,042 basehttp 19476 17632 "OPTIONS /api/ HTTP/1.1" 200 0
WARNING 2025-06-06 04:54:36,078 log 19476 17632 Not Found: /api/
WARNING 2025-06-06 04:54:36,081 log 19476 5008 Not Found: /api/auth/
WARNING 2025-06-06 04:54:36,082 basehttp 19476 17632 "GET /api/ HTTP/1.1" 404 2793
WARNING 2025-06-06 04:54:36,082 basehttp 19476 5008 "OPTIONS /api/auth/ HTTP/1.1" 404 3611
WARNING 2025-06-06 04:54:36,421 log 19476 8268 Unauthorized: /api/ai/health/status/
WARNING 2025-06-06 04:54:36,427 basehttp 19476 8268 "GET /api/ai/health/status/ HTTP/1.1" 401 58
WARNING 2025-06-06 04:54:36,445 log 19476 17632 Not Found: /api/auth/
WARNING 2025-06-06 04:54:36,449 log 19476 5008 Not Found: /api/
WARNING 2025-06-06 04:54:36,449 basehttp 19476 17632 "OPTIONS /api/auth/ HTTP/1.1" 404 3611
WARNING 2025-06-06 04:54:36,450 basehttp 19476 5008 "GET /api/ HTTP/1.1" 404 2793
INFO 2025-06-06 04:56:28,439 basehttp 19476 1392 "OPTIONS /api/events/ HTTP/1.1" 200 0
INFO 2025-06-06 04:56:28,440 basehttp 19476 1392 "OPTIONS /api/events/ HTTP/1.1" 200 0
INFO 2025-06-06 04:57:43,915 basehttp 19476 5008 "POST /api/auth/login/ HTTP/1.1" 200 834
INFO 2025-06-06 04:59:25,307 basehttp 19476 18524 "OPTIONS /api/auth/logout/ HTTP/1.1" 200 0
WARNING 2025-06-06 04:59:25,329 log 19476 18524 Bad Request: /api/auth/logout/
WARNING 2025-06-06 04:59:25,330 basehttp 19476 18524 "POST /api/auth/logout/ HTTP/1.1" 400 25
INFO 2025-06-06 05:01:36,495 basehttp 19476 15392 "POST /api/auth/login/ HTTP/1.1" 200 824
INFO 2025-06-06 05:31:14,202 basehttp 19476 17348 "OPTIONS /api/auth/ HTTP/1.1" 200 0
INFO 2025-06-06 05:31:14,209 basehttp 19476 12696 "OPTIONS /api/ HTTP/1.1" 200 0
INFO 2025-06-06 05:31:14,251 basehttp 19476 18088 "OPTIONS /api/ai/health/status/ HTTP/1.1" 200 0
WARNING 2025-06-06 05:31:14,343 log 19476 18088 Unauthorized: /api/ai/health/status/
WARNING 2025-06-06 05:31:14,344 basehttp 19476 18088 "GET /api/ai/health/status/ HTTP/1.1" 401 58
WARNING 2025-06-06 05:31:14,377 log 19476 17348 Not Found: /api/
WARNING 2025-06-06 05:31:14,391 log 19476 12696 Not Found: /api/auth/
WARNING 2025-06-06 05:31:14,391 basehttp 19476 17348 "GET /api/ HTTP/1.1" 404 2793
WARNING 2025-06-06 05:31:14,392 basehttp 19476 12696 "OPTIONS /api/auth/ HTTP/1.1" 404 3611
INFO 2025-06-06 05:34:36,472 basehttp 19476 12696 "OPTIONS /api/ HTTP/1.1" 200 0
INFO 2025-06-06 05:34:36,473 basehttp 19476 17348 "OPTIONS /api/auth/ HTTP/1.1" 200 0
INFO 2025-06-06 05:34:36,474 basehttp 19476 18088 "OPTIONS /api/ai/health/status/ HTTP/1.1" 200 0
WARNING 2025-06-06 05:34:36,489 log 19476 12696 Unauthorized: /api/ai/health/status/
WARNING 2025-06-06 05:34:36,497 basehttp 19476 12696 "GET /api/ai/health/status/ HTTP/1.1" 401 58
WARNING 2025-06-06 05:34:36,513 log 19476 18088 Not Found: /api/
WARNING 2025-06-06 05:34:36,514 basehttp 19476 18088 "GET /api/ HTTP/1.1" 404 2793
WARNING 2025-06-06 05:34:36,524 log 19476 17348 Not Found: /api/auth/
WARNING 2025-06-06 05:34:36,525 basehttp 19476 17348 "OPTIONS /api/auth/ HTTP/1.1" 404 3611
WARNING 2025-06-06 05:37:02,463 log 19476 20980 Unauthorized: /api/ai/health/status/
WARNING 2025-06-06 05:37:02,474 basehttp 19476 20980 "GET /api/ai/health/status/ HTTP/1.1" 401 58
WARNING 2025-06-06 05:37:02,492 log 19476 13936 Not Found: /api/
WARNING 2025-06-06 05:37:02,495 basehttp 19476 13936 "GET /api/ HTTP/1.1" 404 2793
WARNING 2025-06-06 05:37:02,507 log 19476 13792 Not Found: /api/auth/
WARNING 2025-06-06 05:37:02,508 basehttp 19476 13792 "OPTIONS /api/auth/ HTTP/1.1" 404 3611
INFO 2025-06-06 05:39:09,333 basehttp 19476 13792 "OPTIONS /api/auth/login/ HTTP/1.1" 200 0
INFO 2025-06-06 05:39:09,783 basehttp 19476 13792 "POST /api/auth/login/ HTTP/1.1" 200 834
INFO 2025-06-06 05:42:57,485 basehttp 19476 11512 "OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
INFO 2025-06-06 05:42:57,495 basehttp 19476 20528 "OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
INFO 2025-06-06 05:42:57,511 basehttp 19476 11512 "GET /api/auth/profile/ HTTP/1.1" 200 300
INFO 2025-06-06 05:42:57,529 basehttp 19476 20528 "GET /api/auth/profile/ HTTP/1.1" 200 300
WARNING 2025-06-06 05:42:57,540 log 19476 11512 Unauthorized: /api/ai/health/status/
WARNING 2025-06-06 05:42:57,541 basehttp 19476 11512 "GET /api/ai/health/status/ HTTP/1.1" 401 58
WARNING 2025-06-06 05:42:57,561 log 19476 20528 Not Found: /api/auth/
WARNING 2025-06-06 05:42:57,564 log 19476 2184 Not Found: /api/
WARNING 2025-06-06 05:42:57,564 basehttp 19476 20528 "OPTIONS /api/auth/ HTTP/1.1" 404 3611
WARNING 2025-06-06 05:42:57,564 basehttp 19476 2184 "GET /api/ HTTP/1.1" 404 2793
WARNING 2025-06-06 05:44:09,429 log 19476 10872 Unauthorized: /api/ai/health/status/
WARNING 2025-06-06 05:44:09,434 basehttp 19476 10872 "GET /api/ai/health/status/ HTTP/1.1" 401 58
WARNING 2025-06-06 05:44:09,452 log 19476 17544 Not Found: /api/auth/
WARNING 2025-06-06 05:44:09,453 basehttp 19476 17544 "OPTIONS /api/auth/ HTTP/1.1" 404 3611
WARNING 2025-06-06 05:44:09,461 log 19476 14880 Not Found: /api/
WARNING 2025-06-06 05:44:09,463 basehttp 19476 14880 "GET /api/ HTTP/1.1" 404 2793
INFO 2025-06-06 05:44:11,395 basehttp 19476 14880 "GET /api/auth/profile/ HTTP/1.1" 200 300
INFO 2025-06-06 05:44:11,409 basehttp 19476 17544 "GET /api/auth/profile/ HTTP/1.1" 200 300
WARNING 2025-06-06 05:44:11,539 log 19476 10872 Unauthorized: /api/ai/health/status/
WARNING 2025-06-06 05:44:11,548 basehttp 19476 10872 "GET /api/ai/health/status/ HTTP/1.1" 401 58
WARNING 2025-06-06 05:44:11,563 log 19476 17544 Not Found: /api/auth/
WARNING 2025-06-06 05:44:11,565 log 19476 14880 Not Found: /api/
WARNING 2025-06-06 05:44:11,566 basehttp 19476 17544 "OPTIONS /api/auth/ HTTP/1.1" 404 3611
WARNING 2025-06-06 05:44:11,566 basehttp 19476 14880 "GET /api/ HTTP/1.1" 404 2793
INFO 2025-06-06 05:45:00,027 basehttp 19476 10252 "OPTIONS /api/auth/logout/ HTTP/1.1" 200 0
WARNING 2025-06-06 05:45:00,041 log 19476 10252 Bad Request: /api/auth/logout/
WARNING 2025-06-06 05:45:00,041 basehttp 19476 10252 "POST /api/auth/logout/ HTTP/1.1" 400 25
INFO 2025-06-06 05:45:06,889 basehttp 19476 10252 "POST /api/auth/login/ HTTP/1.1" 200 834
INFO 2025-06-06 05:47:02,565 basehttp 19476 4224 "GET /api/auth/profile/ HTTP/1.1" 200 300
INFO 2025-06-06 05:47:02,585 basehttp 19476 7268 "GET /api/auth/profile/ HTTP/1.1" 200 300
WARNING 2025-06-06 05:47:02,609 log 19476 14296 Unauthorized: /api/ai/health/status/
WARNING 2025-06-06 05:47:02,610 basehttp 19476 14296 "GET /api/ai/health/status/ HTTP/1.1" 401 58
WARNING 2025-06-06 05:47:02,625 log 19476 7268 Not Found: /api/auth/
WARNING 2025-06-06 05:47:02,627 log 19476 4224 Not Found: /api/
WARNING 2025-06-06 05:47:02,628 basehttp 19476 7268 "OPTIONS /api/auth/ HTTP/1.1" 404 3611
WARNING 2025-06-06 05:47:02,628 basehttp 19476 4224 "GET /api/ HTTP/1.1" 404 2793
INFO 2025-06-06 06:08:00,358 autoreload 8824 13592 Watching for file changes with StatReloader
INFO 2025-06-06 06:16:38,323 autoreload 8824 13592 C:\Users\<USER>\OneDrive\Desktop\HMS\backend\emergency_system\views.py changed, reloading.
INFO 2025-06-06 06:16:42,597 autoreload 20924 8524 Watching for file changes with StatReloader
INFO 2025-06-06 06:17:00,284 autoreload 20924 8524 C:\Users\<USER>\OneDrive\Desktop\HMS\backend\emergency_system\views.py changed, reloading.
INFO 2025-06-06 06:17:04,275 autoreload 9568 18840 Watching for file changes with StatReloader
INFO 2025-06-06 06:17:20,409 autoreload 9568 18840 C:\Users\<USER>\OneDrive\Desktop\HMS\backend\emergency_system\urls.py changed, reloading.
INFO 2025-06-06 06:17:24,037 autoreload 17840 1384 Watching for file changes with StatReloader
INFO 2025-06-06 06:18:03,134 autoreload 17840 1384 C:\Users\<USER>\OneDrive\Desktop\HMS\backend\communications\views.py changed, reloading.
INFO 2025-06-06 06:18:07,090 autoreload 4696 15236 Watching for file changes with StatReloader
INFO 2025-06-06 06:18:22,317 autoreload 4696 15236 C:\Users\<USER>\OneDrive\Desktop\HMS\backend\communications\views.py changed, reloading.
INFO 2025-06-06 06:18:26,364 autoreload 17420 17796 Watching for file changes with StatReloader
INFO 2025-06-06 06:18:49,034 autoreload 17420 17796 C:\Users\<USER>\OneDrive\Desktop\HMS\backend\communications\views.py changed, reloading.
INFO 2025-06-06 06:18:53,049 autoreload 13360 21008 Watching for file changes with StatReloader
INFO 2025-06-06 06:19:05,385 autoreload 13360 21008 C:\Users\<USER>\OneDrive\Desktop\HMS\backend\communications\urls.py changed, reloading.
INFO 2025-06-06 06:19:09,139 autoreload 19652 7692 Watching for file changes with StatReloader
ERROR 2025-06-06 06:26:36,971 exception 13160 9256 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\http\request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-06-06 06:26:42,379 log 13160 9256 Bad Request: /api/emergency/contacts/
ERROR 2025-06-06 06:26:42,380 exception 13160 9256 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\http\request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-06-06 06:26:42,400 log 13160 9256 Bad Request: /api/communications/notifications/
ERROR 2025-06-06 06:26:42,401 exception 13160 9256 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\http\request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-06-06 06:26:42,423 log 13160 9256 Bad Request: /api/communications/messages/
ERROR 2025-06-06 06:26:42,424 exception 13160 9256 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\http\request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-06-06 06:26:42,450 log 13160 9256 Bad Request: /api/patients/patients/
ERROR 2025-06-06 06:26:42,451 exception 13160 9256 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\http\request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-06-06 06:26:42,471 log 13160 9256 Bad Request: /api/users/users/
ERROR 2025-06-06 06:26:42,472 exception 13160 9256 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\http\request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-06-06 06:26:42,493 log 13160 9256 Bad Request: /api/appointments/appointments/
ERROR 2025-06-06 06:27:26,819 exception 13944 1820 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\http\request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-06-06 06:27:26,856 log 13944 1820 Bad Request: /api/emergency/contacts/
ERROR 2025-06-06 06:27:27,791 exception 13944 1820 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\http\request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-06-06 06:27:27,821 log 13944 1820 Bad Request: /api/communications/notifications/
ERROR 2025-06-06 06:28:03,039 exception 4948 8700 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\http\request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-06-06 06:28:08,642 log 4948 8700 Bad Request: /api/emergency/contacts/
ERROR 2025-06-06 06:28:08,644 exception 4948 8700 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\http\request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-06-06 06:28:08,664 log 4948 8700 Bad Request: /api/communications/notifications/
ERROR 2025-06-06 06:28:08,669 exception 4948 8700 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\http\request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-06-06 06:28:08,692 log 4948 8700 Bad Request: /api/communications/messages/
ERROR 2025-06-06 06:28:08,693 exception 4948 8700 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\http\request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-06-06 06:28:08,720 log 4948 8700 Bad Request: /api/communications/announcements/
ERROR 2025-06-06 06:28:08,721 exception 4948 8700 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\http\request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-06-06 06:28:08,743 log 4948 8700 Bad Request: /api/patients/patients/
ERROR 2025-06-06 06:28:08,744 exception 4948 8700 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\http\request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-06-06 06:28:08,764 log 4948 8700 Bad Request: /api/users/users/
ERROR 2025-06-06 06:28:09,351 exception 4948 8700 Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\deprecation.py", line 133, in __call__
    response = self.process_request(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\middleware\common.py", line 48, in process_request
    host = request.get_host()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\http\request.py", line 150, in get_host
    raise DisallowedHost(msg)
django.core.exceptions.DisallowedHost: Invalid HTTP_HOST header: 'testserver'. You may need to add 'testserver' to ALLOWED_HOSTS.
WARNING 2025-06-06 06:28:09,384 log 4948 8700 Bad Request: /api/emergency/contacts/
INFO 2025-06-06 06:28:37,657 autoreload 19652 7692 C:\Users\<USER>\OneDrive\Desktop\HMS\backend\hms\settings.py changed, reloading.
INFO 2025-06-06 06:28:41,399 autoreload 2028 4652 Watching for file changes with StatReloader
WARNING 2025-06-06 06:29:42,578 log 14744 18348 Not Found: /api/billing/bills/
INFO 2025-06-06 06:31:07,606 autoreload 2028 4652 C:\Users\<USER>\OneDrive\Desktop\HMS\backend\communications\views.py changed, reloading.
INFO 2025-06-06 06:31:11,290 autoreload 13508 11928 Watching for file changes with StatReloader
WARNING 2025-06-06 06:45:43,857 log 17088 17120 Forbidden: /api/users/users/
WARNING 2025-06-06 06:45:43,916 log 17088 17120 Bad Request: /api/patients/patients/
ERROR 2025-06-06 06:45:44,074 log 17088 17120 Internal Server Error: /api/medical/wards/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\viewsets.py", line 124, in view
    return self.dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\generics.py", line 154, in filter_queryset
    queryset = backend().filter_queryset(self.request, queryset, self)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django_filters\rest_framework\backends.py", line 68, in filter_queryset
    filterset = self.get_filterset(request, queryset, view)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django_filters\rest_framework\backends.py", line 20, in get_filterset
    filterset_class = self.get_filterset_class(view, queryset)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django_filters\rest_framework\backends.py", line 51, in get_filterset_class
    class AutoFilterSet(self.filterset_base):
    ...<2 lines>...
            fields = filterset_fields
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django_filters\filterset.py", line 62, in __new__
    new_class.base_filters = new_class.get_filters()
                             ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django_filters\filterset.py", line 349, in get_filters
    raise TypeError(
    ...<2 lines>...
    )
TypeError: 'Meta.fields' must not contain non-model field names: floor
ERROR 2025-06-06 06:45:44,116 log 17088 17120 Internal Server Error: /api/medical/beds/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\viewsets.py", line 124, in view
    return self.dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\mixins.py", line 38, in list
    queryset = self.filter_queryset(self.get_queryset())
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\generics.py", line 154, in filter_queryset
    queryset = backend().filter_queryset(self.request, queryset, self)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django_filters\rest_framework\backends.py", line 68, in filter_queryset
    filterset = self.get_filterset(request, queryset, view)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django_filters\rest_framework\backends.py", line 20, in get_filterset
    filterset_class = self.get_filterset_class(view, queryset)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django_filters\rest_framework\backends.py", line 51, in get_filterset_class
    class AutoFilterSet(self.filterset_base):
    ...<2 lines>...
            fields = filterset_fields
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django_filters\filterset.py", line 62, in __new__
    new_class.base_filters = new_class.get_filters()
                             ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django_filters\filterset.py", line 349, in get_filters
    raise TypeError(
    ...<2 lines>...
    )
TypeError: 'Meta.fields' must not contain non-model field names: is_occupied, is_available
WARNING 2025-06-06 06:45:44,143 log 17088 17120 Not Found: /api/staff/profiles/
WARNING 2025-06-06 06:45:44,156 log 17088 17120 Bad Request: /api/emergency/contacts/
WARNING 2025-06-06 06:45:44,178 log 17088 17120 Bad Request: /api/communications/notifications/
INFO 2025-06-06 06:46:10,902 autoreload 13508 11928 C:\Users\<USER>\OneDrive\Desktop\HMS\backend\medical_system\serializers.py changed, reloading.
INFO 2025-06-06 06:46:15,158 autoreload 11440 1736 Watching for file changes with StatReloader
INFO 2025-06-06 06:46:25,651 autoreload 11440 1736 C:\Users\<USER>\OneDrive\Desktop\HMS\backend\medical_system\serializers.py changed, reloading.
INFO 2025-06-06 06:46:29,349 autoreload 9396 20692 Watching for file changes with StatReloader
INFO 2025-06-06 06:46:47,121 autoreload 9396 20692 C:\Users\<USER>\OneDrive\Desktop\HMS\backend\medical_system\views.py changed, reloading.
INFO 2025-06-06 06:46:50,948 autoreload 3668 13668 Watching for file changes with StatReloader
INFO 2025-06-06 06:47:16,802 autoreload 3668 13668 C:\Users\<USER>\OneDrive\Desktop\HMS\backend\medical_system\views.py changed, reloading.
INFO 2025-06-06 06:47:20,267 autoreload 20420 13380 Watching for file changes with StatReloader
INFO 2025-06-06 06:47:29,283 autoreload 20420 13380 C:\Users\<USER>\OneDrive\Desktop\HMS\backend\medical_system\views.py changed, reloading.
INFO 2025-06-06 06:47:32,700 autoreload 1472 17720 Watching for file changes with StatReloader
WARNING 2025-06-06 06:48:38,004 log 11324 1996 Forbidden: /api/users/users/
WARNING 2025-06-06 06:48:38,077 log 11324 1996 Bad Request: /api/patients/patients/
WARNING 2025-06-06 06:48:38,111 log 11324 1996 Bad Request: /api/inventory/categories/
WARNING 2025-06-06 06:48:38,167 log 11324 1996 Bad Request: /api/emergency/contacts/
WARNING 2025-06-06 06:48:38,192 log 11324 1996 Bad Request: /api/communications/notifications/
WARNING 2025-06-06 06:49:13,649 log 10020 11100 Forbidden: /api/users/users/
WARNING 2025-06-06 06:49:13,719 log 10020 11100 Bad Request: /api/patients/patients/
WARNING 2025-06-06 06:49:13,750 log 10020 11100 Bad Request: /api/inventory/categories/
WARNING 2025-06-06 06:49:13,805 log 10020 11100 Bad Request: /api/emergency/contacts/
WARNING 2025-06-06 06:49:13,826 log 10020 11100 Bad Request: /api/communications/notifications/
ERROR 2025-06-06 06:49:13,905 log 10020 11100 Internal Server Error: /api/medical/departments/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\viewsets.py", line 124, in view
    return self.dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\mixins.py", line 18, in create
    serializer.is_valid(raise_exception=True)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\serializers.py", line 223, in is_valid
    self._validated_data = self.run_validation(self.initial_data)
                           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\serializers.py", line 442, in run_validation
    value = self.to_internal_value(data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\serializers.py", line 495, in to_internal_value
    for field in fields:
                 ^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\serializers.py", line 378, in _writable_fields
    for field in self.fields.values():
                 ^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ~~~~~~~~~^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\serializers.py", line 372, in fields
    for key, value in self.get_fields().items():
                      ~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\serializers.py", line 1103, in get_fields
    field_class, field_kwargs = self.build_field(
                                ~~~~~~~~~~~~~~~~^
        source, info, model, depth
        ^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\serializers.py", line 1249, in build_field
    return self.build_unknown_field(field_name, model_class)
           ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\serializers.py", line 1367, in build_unknown_field
    raise ImproperlyConfigured(
    ...<2 lines>...
    )
django.core.exceptions.ImproperlyConfigured: Field name `phone_extension` is not valid for model `Department` in `medical_system.serializers.DepartmentSerializer`.
WARNING 2025-06-06 06:54:05,280 log 13000 21352 Forbidden: /api/users/users/
WARNING 2025-06-06 06:54:05,355 log 13000 21352 Bad Request: /api/patients/patients/
WARNING 2025-06-06 06:54:05,382 log 13000 21352 Bad Request: /api/inventory/categories/
WARNING 2025-06-06 06:54:05,432 log 13000 21352 Bad Request: /api/emergency/contacts/
WARNING 2025-06-06 06:54:05,455 log 13000 21352 Bad Request: /api/communications/notifications/
ERROR 2025-06-06 06:54:05,520 log 13000 21352 Internal Server Error: /api/medical/departments/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\viewsets.py", line 124, in view
    return self.dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\mixins.py", line 18, in create
    serializer.is_valid(raise_exception=True)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\serializers.py", line 223, in is_valid
    self._validated_data = self.run_validation(self.initial_data)
                           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\serializers.py", line 442, in run_validation
    value = self.to_internal_value(data)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\serializers.py", line 495, in to_internal_value
    for field in fields:
                 ^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\serializers.py", line 378, in _writable_fields
    for field in self.fields.values():
                 ^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\django\utils\functional.py", line 57, in __get__
    res = instance.__dict__[self.name] = self.func(instance)
                                         ~~~~~~~~~^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\serializers.py", line 372, in fields
    for key, value in self.get_fields().items():
                      ~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\serializers.py", line 1103, in get_fields
    field_class, field_kwargs = self.build_field(
                                ~~~~~~~~~~~~~~~~^
        source, info, model, depth
        ^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\serializers.py", line 1249, in build_field
    return self.build_unknown_field(field_name, model_class)
           ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\rest_framework\serializers.py", line 1367, in build_unknown_field
    raise ImproperlyConfigured(
    ...<2 lines>...
    )
django.core.exceptions.ImproperlyConfigured: Field name `phone_extension` is not valid for model `Department` in `medical_system.serializers.DepartmentSerializer`.
WARNING 2025-06-06 06:54:05,527 log 13000 21352 Bad Request: /api/inventory/categories/
INFO 2025-06-06 06:54:56,566 autoreload 1472 17720 C:\Users\<USER>\OneDrive\Desktop\HMS\backend\medical_system\serializers.py changed, reloading.
INFO 2025-06-06 06:55:00,833 autoreload 6368 3864 Watching for file changes with StatReloader
WARNING 2025-06-06 06:55:43,402 log 11320 13148 Forbidden: /api/users/users/
WARNING 2025-06-06 06:58:28,109 log 2896 17796 Forbidden: /api/users/users/
WARNING 2025-06-06 06:58:28,142 log 2896 17796 Bad Request: /api/medical/departments/
WARNING 2025-06-06 06:58:28,194 log 2896 17796 Bad Request: /api/inventory/categories/
WARNING 2025-06-06 07:00:56,324 log 6680 8892 Not Found: /api/appointments/appointment-slots/
INFO 2025-06-06 07:15:26,282 basehttp 6368 15020 "OPTIONS /api/users/users/me/ HTTP/1.1" 200 0
INFO 2025-06-06 07:15:26,287 basehttp 6368 2656 "OPTIONS /api/users/language/ HTTP/1.1" 200 0
WARNING 2025-06-06 07:15:26,613 log 6368 17128 Unauthorized: /api/users/users/me/
WARNING 2025-06-06 07:15:26,615 basehttp 6368 17128 "GET /api/users/users/me/ HTTP/1.1" 401 58
WARNING 2025-06-06 07:15:26,697 log 6368 18476 Not Found: /api/users/language/
WARNING 2025-06-06 07:15:26,698 basehttp 6368 18476 "GET /api/users/language/ HTTP/1.1" 404 13346
INFO 2025-06-06 07:19:01,060 autoreload 7664 20652 Watching for file changes with StatReloader
WARNING 2025-06-06 07:20:25,128 log 6368 18844 Unauthorized: /api/ai/conversations/
WARNING 2025-06-06 07:20:25,129 basehttp 6368 18844 "GET /api/ai/conversations/ HTTP/1.1" 401 58
INFO 2025-06-06 07:24:21,667 basehttp 6368 18804 "OPTIONS /api/ HTTP/1.1" 200 0
INFO 2025-06-06 07:24:21,667 basehttp 6368 10736 "OPTIONS /api/auth/ HTTP/1.1" 200 0
INFO 2025-06-06 07:24:21,670 basehttp 6368 20844 "OPTIONS /api/ai/health/status/ HTTP/1.1" 200 0
WARNING 2025-06-06 07:24:21,685 log 6368 18804 Unauthorized: /api/ai/health/status/
WARNING 2025-06-06 07:24:21,695 basehttp 6368 18804 "GET /api/ai/health/status/ HTTP/1.1" 401 58
WARNING 2025-06-06 07:24:21,709 log 6368 20844 Not Found: /api/
WARNING 2025-06-06 07:24:21,710 basehttp 6368 20844 "GET /api/ HTTP/1.1" 404 3701
WARNING 2025-06-06 07:24:21,715 log 6368 10736 Not Found: /api/auth/
WARNING 2025-06-06 07:24:21,716 basehttp 6368 10736 "OPTIONS /api/auth/ HTTP/1.1" 404 4519
INFO 2025-06-06 07:24:40,447 basehttp 6368 10736 "OPTIONS /api/auth/login/ HTTP/1.1" 200 0
INFO 2025-06-06 07:24:41,088 basehttp 6368 10736 "POST /api/auth/login/ HTTP/1.1" 200 804
INFO 2025-06-06 07:24:50,812 basehttp 6368 3264 "OPTIONS /api/ai/conversations/chat/ HTTP/1.1" 200 0
WARNING 2025-06-06 07:24:50,814 log 6368 3264 Unauthorized: /api/ai/conversations/chat/
WARNING 2025-06-06 07:24:50,815 basehttp 6368 3264 "POST /api/ai/conversations/chat/ HTTP/1.1" 401 58
WARNING 2025-06-06 07:24:53,313 log 6368 3264 Unauthorized: /api/ai/health/status/
WARNING 2025-06-06 07:24:53,314 log 6368 15016 Unauthorized: /api/ai/conversations/
WARNING 2025-06-06 07:24:53,314 basehttp 6368 3264 "GET /api/ai/health/status/ HTTP/1.1" 401 58
WARNING 2025-06-06 07:24:53,315 basehttp 6368 15016 "GET /api/ai/conversations/ HTTP/1.1" 401 58
WARNING 2025-06-06 07:24:53,318 log 6368 3264 Unauthorized: /api/ai/health/status/
WARNING 2025-06-06 07:24:53,319 log 6368 15016 Unauthorized: /api/ai/conversations/
WARNING 2025-06-06 07:24:53,320 basehttp 6368 15016 "GET /api/ai/conversations/ HTTP/1.1" 401 58
WARNING 2025-06-06 07:24:53,320 basehttp 6368 3264 "GET /api/ai/health/status/ HTTP/1.1" 401 58
INFO 2025-06-06 07:25:22,538 basehttp 6368 15016 "OPTIONS /api/ai/diagnoses/generate_diagnosis/ HTTP/1.1" 200 0
WARNING 2025-06-06 07:25:22,541 log 6368 15016 Unauthorized: /api/ai/diagnoses/generate_diagnosis/
WARNING 2025-06-06 07:25:22,542 basehttp 6368 15016 "POST /api/ai/diagnoses/generate_diagnosis/ HTTP/1.1" 401 58
WARNING 2025-06-06 07:26:09,462 log 6368 15016 Unauthorized: /api/ai/health/status/
WARNING 2025-06-06 07:26:09,464 log 6368 3264 Unauthorized: /api/ai/conversations/
WARNING 2025-06-06 07:26:09,465 basehttp 6368 15016 "GET /api/ai/health/status/ HTTP/1.1" 401 58
WARNING 2025-06-06 07:26:09,465 basehttp 6368 3264 "GET /api/ai/conversations/ HTTP/1.1" 401 58
WARNING 2025-06-06 07:26:09,469 log 6368 3264 Unauthorized: /api/ai/conversations/
WARNING 2025-06-06 07:26:09,472 log 6368 15016 Unauthorized: /api/ai/health/status/
WARNING 2025-06-06 07:26:09,472 basehttp 6368 3264 "GET /api/ai/conversations/ HTTP/1.1" 401 58
WARNING 2025-06-06 07:26:09,472 basehttp 6368 15016 "GET /api/ai/health/status/ HTTP/1.1" 401 58
WARNING 2025-06-06 07:28:59,897 log 6368 3264 Unauthorized: /api/ai/health/status/
WARNING 2025-06-06 07:28:59,898 log 6368 15016 Unauthorized: /api/ai/conversations/
WARNING 2025-06-06 07:28:59,899 basehttp 6368 15016 "GET /api/ai/conversations/ HTTP/1.1" 401 58
WARNING 2025-06-06 07:28:59,899 basehttp 6368 3264 "GET /api/ai/health/status/ HTTP/1.1" 401 58
WARNING 2025-06-06 07:28:59,902 log 6368 3264 Unauthorized: /api/ai/health/status/
WARNING 2025-06-06 07:28:59,904 log 6368 15016 Unauthorized: /api/ai/conversations/
WARNING 2025-06-06 07:28:59,904 basehttp 6368 3264 "GET /api/ai/health/status/ HTTP/1.1" 401 58
WARNING 2025-06-06 07:28:59,905 basehttp 6368 15016 "GET /api/ai/conversations/ HTTP/1.1" 401 58
WARNING 2025-06-06 07:30:19,628 log 6368 15016 Unauthorized: /api/ai/health/status/
WARNING 2025-06-06 07:30:19,629 log 6368 3264 Unauthorized: /api/ai/conversations/
WARNING 2025-06-06 07:30:19,630 basehttp 6368 15016 "GET /api/ai/health/status/ HTTP/1.1" 401 58
WARNING 2025-06-06 07:30:19,631 basehttp 6368 3264 "GET /api/ai/conversations/ HTTP/1.1" 401 58
WARNING 2025-06-06 07:30:19,635 log 6368 15016 Unauthorized: /api/ai/health/status/
WARNING 2025-06-06 07:30:19,637 log 6368 3264 Unauthorized: /api/ai/conversations/
WARNING 2025-06-06 07:30:19,638 basehttp 6368 3264 "GET /api/ai/conversations/ HTTP/1.1" 401 58
WARNING 2025-06-06 07:30:19,638 basehttp 6368 15016 "GET /api/ai/health/status/ HTTP/1.1" 401 58
WARNING 2025-06-06 07:31:34,979 log 6368 15016 Unauthorized: /api/ai/health/status/
WARNING 2025-06-06 07:31:34,980 log 6368 3264 Unauthorized: /api/ai/conversations/
WARNING 2025-06-06 07:31:34,980 basehttp 6368 15016 "GET /api/ai/health/status/ HTTP/1.1" 401 58
WARNING 2025-06-06 07:31:34,981 basehttp 6368 3264 "GET /api/ai/conversations/ HTTP/1.1" 401 58
WARNING 2025-06-06 07:31:34,984 log 6368 15016 Unauthorized: /api/ai/health/status/
WARNING 2025-06-06 07:31:34,987 log 6368 3264 Unauthorized: /api/ai/conversations/
WARNING 2025-06-06 07:31:34,989 basehttp 6368 3264 "GET /api/ai/conversations/ HTTP/1.1" 401 58
WARNING 2025-06-06 07:31:34,990 basehttp 6368 15016 "GET /api/ai/health/status/ HTTP/1.1" 401 58
WARNING 2025-06-06 07:31:44,020 log 6368 3264 Unauthorized: /api/ai/health/status/
WARNING 2025-06-06 07:31:44,023 log 6368 15016 Unauthorized: /api/ai/conversations/
WARNING 2025-06-06 07:31:44,024 basehttp 6368 3264 "GET /api/ai/health/status/ HTTP/1.1" 401 58
WARNING 2025-06-06 07:31:44,025 basehttp 6368 15016 "GET /api/ai/conversations/ HTTP/1.1" 401 58
WARNING 2025-06-06 07:31:44,028 log 6368 13820 Unauthorized: /api/ai/health/status/
WARNING 2025-06-06 07:31:44,030 log 6368 3264 Unauthorized: /api/ai/conversations/
WARNING 2025-06-06 07:31:44,031 basehttp 6368 13820 "GET /api/ai/health/status/ HTTP/1.1" 401 58
WARNING 2025-06-06 07:31:44,033 basehttp 6368 3264 "GET /api/ai/conversations/ HTTP/1.1" 401 58
INFO 2025-06-06 07:32:52,684 basehttp 6368 20688 "OPTIONS /api/patients/?page=1&page_size=10 HTTP/1.1" 200 0
INFO 2025-06-06 07:32:52,701 basehttp 6368 11428 "OPTIONS /api/patients/?page=1&page_size=10 HTTP/1.1" 200 0
INFO 2025-06-06 07:32:52,885 basehttp 6368 20688 "GET /api/patients/?page=1&page_size=10 HTTP/1.1" 200 335
INFO 2025-06-06 07:32:52,903 basehttp 6368 11428 "GET /api/patients/?page=1&page_size=10 HTTP/1.1" 200 335
INFO 2025-06-06 07:39:31,797 basehttp 6368 17504 "POST /api/auth/login/ HTTP/1.1" 200 804
WARNING 2025-06-06 07:39:38,427 log 6368 20760 Unauthorized: /api/ai/health/status/
WARNING 2025-06-06 07:39:38,430 log 6368 9644 Unauthorized: /api/ai/conversations/
WARNING 2025-06-06 07:39:38,430 basehttp 6368 20760 "GET /api/ai/health/status/ HTTP/1.1" 401 58
WARNING 2025-06-06 07:39:38,431 basehttp 6368 9644 "GET /api/ai/conversations/ HTTP/1.1" 401 58
WARNING 2025-06-06 07:39:38,440 log 6368 20760 Unauthorized: /api/ai/health/status/
WARNING 2025-06-06 07:39:38,443 log 6368 9644 Unauthorized: /api/ai/conversations/
WARNING 2025-06-06 07:39:38,444 basehttp 6368 20760 "GET /api/ai/health/status/ HTTP/1.1" 401 58
WARNING 2025-06-06 07:39:38,444 basehttp 6368 9644 "GET /api/ai/conversations/ HTTP/1.1" 401 58
INFO 2025-06-06 07:39:47,313 basehttp 6368 17504 "GET /api/patients/?page=1&page_size=10 HTTP/1.1" 200 335
INFO 2025-06-06 07:39:47,323 basehttp 6368 5520 "GET /api/patients/?page=1&page_size=10 HTTP/1.1" 200 335
WARNING 2025-06-06 07:44:26,256 log 6368 12724 Unauthorized: /api/ai/health/status/
WARNING 2025-06-06 07:44:26,257 basehttp 6368 12724 "GET /api/ai/health/status/ HTTP/1.1" 401 58
WARNING 2025-06-06 07:44:26,259 log 6368 19888 Unauthorized: /api/ai/conversations/
WARNING 2025-06-06 07:44:26,261 log 6368 12724 Unauthorized: /api/ai/health/status/
WARNING 2025-06-06 07:44:26,261 basehttp 6368 19888 "GET /api/ai/conversations/ HTTP/1.1" 401 58
WARNING 2025-06-06 07:44:26,261 basehttp 6368 12724 "GET /api/ai/health/status/ HTTP/1.1" 401 58
WARNING 2025-06-06 07:44:26,264 log 6368 19888 Unauthorized: /api/ai/conversations/
WARNING 2025-06-06 07:44:26,264 basehttp 6368 19888 "GET /api/ai/conversations/ HTTP/1.1" 401 58
INFO 2025-06-06 07:44:30,406 basehttp 6368 13744 "GET /api/patients/?page=1&page_size=10 HTTP/1.1" 200 335
INFO 2025-06-06 07:44:30,421 basehttp 6368 4696 "GET /api/patients/?page=1&page_size=10 HTTP/1.1" 200 335
INFO 2025-06-06 07:45:10,365 basehttp 6368 4696 "GET /api/patients/?page=1&page_size=10 HTTP/1.1" 200 335
INFO 2025-06-06 07:45:10,371 basehttp 6368 13744 "GET /api/patients/?page=1&page_size=10 HTTP/1.1" 200 335
WARNING 2025-06-06 07:45:32,366 log 6368 19888 Unauthorized: /api/ai/health/status/
WARNING 2025-06-06 07:45:32,367 log 6368 12724 Unauthorized: /api/ai/conversations/
WARNING 2025-06-06 07:45:32,368 basehttp 6368 19888 "GET /api/ai/health/status/ HTTP/1.1" 401 58
WARNING 2025-06-06 07:45:32,370 basehttp 6368 12724 "GET /api/ai/conversations/ HTTP/1.1" 401 58
WARNING 2025-06-06 07:45:32,372 log 6368 19888 Unauthorized: /api/ai/health/status/
WARNING 2025-06-06 07:45:32,375 log 6368 12724 Unauthorized: /api/ai/conversations/
WARNING 2025-06-06 07:45:32,377 basehttp 6368 19888 "GET /api/ai/health/status/ HTTP/1.1" 401 58
WARNING 2025-06-06 07:45:32,382 basehttp 6368 12724 "GET /api/ai/conversations/ HTTP/1.1" 401 58
INFO 2025-06-06 07:45:34,273 basehttp 6368 13744 "GET /api/patients/?page=1&page_size=10 HTTP/1.1" 200 335
INFO 2025-06-06 07:45:34,286 basehttp 6368 4696 "GET /api/patients/?page=1&page_size=10 HTTP/1.1" 200 335
INFO 2025-06-06 08:00:33,473 basehttp 6368 14856 "OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
INFO 2025-06-06 08:00:33,498 basehttp 6368 2836 "OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
INFO 2025-06-06 08:00:33,540 basehttp 6368 14856 "GET /api/auth/profile/ HTTP/1.1" 200 273
INFO 2025-06-06 08:00:33,572 basehttp 6368 2836 "GET /api/auth/profile/ HTTP/1.1" 200 273
WARNING 2025-06-06 08:00:35,712 log 6368 14052 Unauthorized: /api/ai/health/status/
WARNING 2025-06-06 08:00:35,731 basehttp 6368 14052 "GET /api/ai/health/status/ HTTP/1.1" 401 58
WARNING 2025-06-06 08:00:35,854 log 6368 2836 Not Found: /api/
WARNING 2025-06-06 08:00:35,856 basehttp 6368 2836 "GET /api/ HTTP/1.1" 404 3701
WARNING 2025-06-06 08:00:35,861 log 6368 14856 Not Found: /api/auth/
WARNING 2025-06-06 08:00:35,862 basehttp 6368 14856 "OPTIONS /api/auth/ HTTP/1.1" 404 4519
WARNING 2025-06-06 08:00:38,578 log 6368 20448 Unauthorized: /api/ai/health/status/
WARNING 2025-06-06 08:00:38,579 basehttp 6368 20448 "GET /api/ai/health/status/ HTTP/1.1" 401 58
WARNING 2025-06-06 08:00:38,582 log 6368 15404 Unauthorized: /api/ai/conversations/
WARNING 2025-06-06 08:00:38,590 log 6368 20448 Unauthorized: /api/ai/health/status/
WARNING 2025-06-06 08:00:38,592 basehttp 6368 20448 "GET /api/ai/health/status/ HTTP/1.1" 401 58
WARNING 2025-06-06 08:00:38,592 basehttp 6368 15404 "GET /api/ai/conversations/ HTTP/1.1" 401 58
WARNING 2025-06-06 08:00:38,598 log 6368 15404 Unauthorized: /api/ai/conversations/
WARNING 2025-06-06 08:00:38,599 basehttp 6368 15404 "GET /api/ai/conversations/ HTTP/1.1" 401 58
INFO 2025-06-06 08:00:39,824 basehttp 6368 2836 "OPTIONS /api/users/users/ HTTP/1.1" 200 0
INFO 2025-06-06 08:00:39,825 basehttp 6368 14856 "OPTIONS /api/users/users/ HTTP/1.1" 200 0
WARNING 2025-06-06 08:00:39,840 log 6368 14856 Unauthorized: /api/users/users/
WARNING 2025-06-06 08:00:39,841 basehttp 6368 14856 "GET /api/users/users/ HTTP/1.1" 401 172
WARNING 2025-06-06 08:00:39,846 log 6368 2836 Unauthorized: /api/users/users/
WARNING 2025-06-06 08:00:39,847 basehttp 6368 2836 "GET /api/users/users/ HTTP/1.1" 401 172
WARNING 2025-06-06 08:01:13,242 log 6368 2836 Unauthorized: /api/users/users/
WARNING 2025-06-06 08:01:13,243 basehttp 6368 2836 "POST /api/users/users/ HTTP/1.1" 401 172
INFO 2025-06-06 08:04:43,633 basehttp 6368 2836 "GET /api/auth/profile/ HTTP/1.1" 200 273
INFO 2025-06-06 08:04:43,649 basehttp 6368 14856 "GET /api/auth/profile/ HTTP/1.1" 200 273
WARNING 2025-06-06 08:04:45,445 log 6368 14052 Unauthorized: /api/ai/health/status/
WARNING 2025-06-06 08:04:45,457 basehttp 6368 14052 "GET /api/ai/health/status/ HTTP/1.1" 401 58
WARNING 2025-06-06 08:04:45,480 log 6368 14856 Not Found: /api/auth/
WARNING 2025-06-06 08:04:45,481 basehttp 6368 14856 "OPTIONS /api/auth/ HTTP/1.1" 404 4519
WARNING 2025-06-06 08:04:45,493 log 6368 2836 Not Found: /api/
WARNING 2025-06-06 08:04:45,494 basehttp 6368 2836 "GET /api/ HTTP/1.1" 404 3701
INFO 2025-06-06 08:04:46,333 basehttp 6368 2836 "GET /api/users/users/ HTTP/1.1" 200 10540
INFO 2025-06-06 08:04:46,378 basehttp 6368 14856 "GET /api/users/users/ HTTP/1.1" 200 10540
INFO 2025-06-06 08:08:07,739 basehttp 6368 2896 "GET /api/auth/profile/ HTTP/1.1" 200 273
INFO 2025-06-06 08:08:07,762 basehttp 6368 1968 "GET /api/auth/profile/ HTTP/1.1" 200 273
WARNING 2025-06-06 08:08:09,459 log 6368 1968 Not Found: /api/auth/
WARNING 2025-06-06 08:08:09,462 log 6368 2896 Not Found: /api/
WARNING 2025-06-06 08:08:09,463 basehttp 6368 1968 "OPTIONS /api/auth/ HTTP/1.1" 404 4519
WARNING 2025-06-06 08:08:09,467 basehttp 6368 2896 "GET /api/ HTTP/1.1" 404 3701
WARNING 2025-06-06 08:08:09,477 log 6368 8112 Unauthorized: /api/ai/health/status/
WARNING 2025-06-06 08:08:09,486 basehttp 6368 8112 "GET /api/ai/health/status/ HTTP/1.1" 401 58
INFO 2025-06-06 08:08:11,141 basehttp 6368 8112 "GET /api/users/users/ HTTP/1.1" 200 10540
INFO 2025-06-06 08:08:11,180 basehttp 6368 2896 "GET /api/users/users/ HTTP/1.1" 200 10540
INFO 2025-06-06 08:08:13,297 basehttp 6368 2896 "GET /api/patients/?page=1&page_size=10 HTTP/1.1" 200 335
INFO 2025-06-06 08:08:13,310 basehttp 6368 8112 "GET /api/patients/?page=1&page_size=10 HTTP/1.1" 200 335
INFO 2025-06-06 08:08:30,796 basehttp 6368 8112 "GET /api/users/users/ HTTP/1.1" 200 10540
INFO 2025-06-06 08:08:30,857 basehttp 6368 2896 "GET /api/users/users/ HTTP/1.1" 200 10540
INFO 2025-06-06 08:08:42,220 basehttp 6368 2896 "GET /api/users/users/ HTTP/1.1" 200 10540
INFO 2025-06-06 08:08:42,249 basehttp 6368 8112 "GET /api/users/users/ HTTP/1.1" 200 10540
INFO 2025-06-06 08:08:43,604 basehttp 6368 8112 "GET /api/patients/?page=1&page_size=10 HTTP/1.1" 200 335
INFO 2025-06-06 08:08:43,616 basehttp 6368 2896 "GET /api/patients/?page=1&page_size=10 HTTP/1.1" 200 335
INFO 2025-06-06 08:13:11,712 basehttp 6368 21452 "OPTIONS /api/billing/invoices/ HTTP/1.1" 200 0
INFO 2025-06-06 08:13:11,716 basehttp 6368 20648 "OPTIONS /api/billing/invoices/ HTTP/1.1" 200 0
INFO 2025-06-06 08:13:12,180 basehttp 6368 21452 "GET /api/billing/invoices/ HTTP/1.1" 200 814
INFO 2025-06-06 08:13:12,343 basehttp 6368 20648 "GET /api/billing/invoices/ HTTP/1.1" 200 814
INFO 2025-06-06 08:17:10,173 basehttp 6368 20648 "GET /api/users/users/ HTTP/1.1" 200 10540
INFO 2025-06-06 08:17:10,206 basehttp 6368 21452 "GET /api/users/users/ HTTP/1.1" 200 10540
INFO 2025-06-06 08:17:12,198 basehttp 6368 21452 "GET /api/patients/?page=1&page_size=10 HTTP/1.1" 200 335
INFO 2025-06-06 08:17:12,212 basehttp 6368 20648 "GET /api/patients/?page=1&page_size=10 HTTP/1.1" 200 335
INFO 2025-06-06 08:18:41,921 basehttp 6368 20648 "GET /api/users/users/ HTTP/1.1" 200 10540
INFO 2025-06-06 08:18:41,946 basehttp 6368 21452 "GET /api/users/users/ HTTP/1.1" 200 10540
INFO 2025-06-06 08:18:43,411 basehttp 6368 21452 "GET /api/patients/?page=1&page_size=10 HTTP/1.1" 200 335
INFO 2025-06-06 08:18:43,418 basehttp 6368 20648 "GET /api/patients/?page=1&page_size=10 HTTP/1.1" 200 335
INFO 2025-06-06 08:20:50,484 basehttp 6368 20648 "GET /api/patients/?page=1&page_size=10 HTTP/1.1" 200 335
WARNING 2025-06-14 09:58:08,832 multi_agent_system 3396 4152 Failed to initialize Gemini Flash for Triage Specialist: name 'os' is not defined
INFO 2025-06-14 09:58:08,834 multi_agent_system 3396 4152 Initialized agent: Triage Specialist
WARNING 2025-06-14 09:58:08,835 multi_agent_system 3396 4152 Failed to initialize Gemini Pro for Diagnostic Specialist: name 'os' is not defined
INFO 2025-06-14 09:58:08,835 multi_agent_system 3396 4152 Initialized agent: Diagnostic Specialist
WARNING 2025-06-14 09:58:08,835 multi_agent_system 3396 4152 Failed to initialize Gemini Pro for Treatment Specialist: name 'os' is not defined
INFO 2025-06-14 09:58:08,836 multi_agent_system 3396 4152 Initialized agent: Treatment Specialist
WARNING 2025-06-14 09:58:08,836 multi_agent_system 3396 4152 Failed to initialize Gemini Flash for Clinical Pharmacist: name 'os' is not defined
INFO 2025-06-14 09:58:08,837 multi_agent_system 3396 4152 Initialized agent: Clinical Pharmacist
INFO 2025-06-14 10:02:15,389 multi_agent_system 12316 10724 Initialized agent: Triage Specialist
INFO 2025-06-14 10:02:15,391 multi_agent_system 12316 10724 Initialized agent: Diagnostic Specialist
INFO 2025-06-14 10:02:15,396 multi_agent_system 12316 10724 Initialized agent: Treatment Specialist
INFO 2025-06-14 10:02:15,399 multi_agent_system 12316 10724 Initialized agent: Clinical Pharmacist
INFO 2025-06-14 10:03:15,568 multi_agent_system 7176 3092 Initialized agent: Triage Specialist
INFO 2025-06-14 10:03:15,571 multi_agent_system 7176 3092 Initialized agent: Diagnostic Specialist
INFO 2025-06-14 10:03:15,573 multi_agent_system 7176 3092 Initialized agent: Treatment Specialist
INFO 2025-06-14 10:03:15,574 multi_agent_system 7176 3092 Initialized agent: Clinical Pharmacist
INFO 2025-06-14 10:08:30,365 multi_agent_system 7336 15824 Initialized agent: Triage Specialist
INFO 2025-06-14 10:08:30,368 multi_agent_system 7336 15824 Initialized agent: Diagnostic Specialist
INFO 2025-06-14 10:08:30,371 multi_agent_system 7336 15824 Initialized agent: Treatment Specialist
INFO 2025-06-14 10:08:30,373 multi_agent_system 7336 15824 Initialized agent: Clinical Pharmacist
INFO 2025-06-14 10:08:46,953 multi_agent_system 7708 5412 Initialized agent: Triage Specialist
INFO 2025-06-14 10:08:46,957 multi_agent_system 7708 5412 Initialized agent: Diagnostic Specialist
INFO 2025-06-14 10:08:46,960 multi_agent_system 7708 5412 Initialized agent: Treatment Specialist
INFO 2025-06-14 10:08:46,962 multi_agent_system 7708 5412 Initialized agent: Clinical Pharmacist
INFO 2025-06-14 10:09:06,013 multi_agent_system 8576 7952 Initialized agent: Triage Specialist
INFO 2025-06-14 10:09:06,015 multi_agent_system 8576 7952 Initialized agent: Diagnostic Specialist
INFO 2025-06-14 10:09:06,017 multi_agent_system 8576 7952 Initialized agent: Treatment Specialist
INFO 2025-06-14 10:09:06,019 multi_agent_system 8576 7952 Initialized agent: Clinical Pharmacist
INFO 2025-06-14 10:15:46,653 multi_agent_system 10880 1048 Initialized agent: Triage Specialist
INFO 2025-06-14 10:15:46,655 multi_agent_system 10880 1048 Initialized agent: Diagnostic Specialist
INFO 2025-06-14 10:15:46,657 multi_agent_system 10880 1048 Initialized agent: Treatment Specialist
INFO 2025-06-14 10:15:46,658 multi_agent_system 10880 1048 Initialized agent: Clinical Pharmacist
INFO 2025-06-14 10:16:02,048 multi_agent_system 10220 13016 Initialized agent: Triage Specialist
INFO 2025-06-14 10:16:02,051 multi_agent_system 10220 13016 Initialized agent: Diagnostic Specialist
INFO 2025-06-14 10:16:02,053 multi_agent_system 10220 13016 Initialized agent: Treatment Specialist
INFO 2025-06-14 10:16:02,055 multi_agent_system 10220 13016 Initialized agent: Clinical Pharmacist
INFO 2025-06-14 10:35:04,832 multi_agent_system 5652 3712 Initialized agent: Triage Specialist
INFO 2025-06-14 10:35:04,835 multi_agent_system 5652 3712 Initialized agent: Diagnostic Specialist
INFO 2025-06-14 10:35:04,837 multi_agent_system 5652 3712 Initialized agent: Treatment Specialist
INFO 2025-06-14 10:35:04,838 multi_agent_system 5652 3712 Initialized agent: Clinical Pharmacist
INFO 2025-06-14 10:57:42,351 multi_agent_system 17604 14152 Initialized agent: Triage Specialist
INFO 2025-06-14 10:57:42,354 multi_agent_system 17604 14152 Initialized agent: Diagnostic Specialist
INFO 2025-06-14 10:57:42,356 multi_agent_system 17604 14152 Initialized agent: Treatment Specialist
INFO 2025-06-14 10:57:42,358 multi_agent_system 17604 14152 Initialized agent: Clinical Pharmacist
INFO 2025-06-14 11:00:13,666 multi_agent_system 6464 14884 Initialized agent: Triage Specialist
INFO 2025-06-14 11:00:13,668 multi_agent_system 6464 14884 Initialized agent: Diagnostic Specialist
INFO 2025-06-14 11:00:13,670 multi_agent_system 6464 14884 Initialized agent: Treatment Specialist
INFO 2025-06-14 11:00:13,672 multi_agent_system 6464 14884 Initialized agent: Clinical Pharmacist
INFO 2025-06-14 11:04:06,040 multi_agent_system 5944 14792 Initialized agent: Triage Specialist
INFO 2025-06-14 11:04:06,042 multi_agent_system 5944 14792 Initialized agent: Diagnostic Specialist
INFO 2025-06-14 11:04:06,044 multi_agent_system 5944 14792 Initialized agent: Treatment Specialist
INFO 2025-06-14 11:04:06,046 multi_agent_system 5944 14792 Initialized agent: Clinical Pharmacist
INFO 2025-06-14 11:21:34,216 multi_agent_system 10884 5460 Initialized agent: Triage Specialist
INFO 2025-06-14 11:21:34,219 multi_agent_system 10884 5460 Initialized agent: Diagnostic Specialist
INFO 2025-06-14 11:21:34,221 multi_agent_system 10884 5460 Initialized agent: Treatment Specialist
INFO 2025-06-14 11:21:34,223 multi_agent_system 10884 5460 Initialized agent: Clinical Pharmacist
INFO 2025-06-14 11:22:31,892 multi_agent_system 4916 3148 Initialized agent: Triage Specialist
INFO 2025-06-14 11:22:31,895 multi_agent_system 4916 3148 Initialized agent: Diagnostic Specialist
INFO 2025-06-14 11:22:31,898 multi_agent_system 4916 3148 Initialized agent: Treatment Specialist
INFO 2025-06-14 11:22:31,900 multi_agent_system 4916 3148 Initialized agent: Clinical Pharmacist
INFO 2025-06-14 11:23:57,671 multi_agent_system 2016 17056 Initialized agent: Triage Specialist
INFO 2025-06-14 11:23:57,674 multi_agent_system 2016 17056 Initialized agent: Diagnostic Specialist
INFO 2025-06-14 11:23:57,676 multi_agent_system 2016 17056 Initialized agent: Treatment Specialist
INFO 2025-06-14 11:23:57,677 multi_agent_system 2016 17056 Initialized agent: Clinical Pharmacist
INFO 2025-06-14 11:31:06,793 multi_agent_system 16476 7280 Initialized agent: Triage Specialist
INFO 2025-06-14 11:31:06,795 multi_agent_system 16476 7280 Initialized agent: Diagnostic Specialist
INFO 2025-06-14 11:31:06,799 multi_agent_system 16476 7280 Initialized agent: Treatment Specialist
INFO 2025-06-14 11:31:06,801 multi_agent_system 16476 7280 Initialized agent: Clinical Pharmacist
WARNING 2025-06-14 11:31:07,554 log 16476 7280 Method Not Allowed: /api/appointments/
WARNING 2025-06-14 11:31:07,562 log 16476 7280 Not Found: /api/users/99999/
INFO 2025-06-14 11:32:15,492 multi_agent_system 9476 10992 Initialized agent: Triage Specialist
INFO 2025-06-14 11:32:15,495 multi_agent_system 9476 10992 Initialized agent: Diagnostic Specialist
INFO 2025-06-14 11:32:15,497 multi_agent_system 9476 10992 Initialized agent: Treatment Specialist
INFO 2025-06-14 11:32:15,498 multi_agent_system 9476 10992 Initialized agent: Clinical Pharmacist
WARNING 2025-06-14 11:32:16,775 log 9476 10992 Method Not Allowed: /api/appointments/
INFO 2025-06-14 11:34:26,384 multi_agent_system 5584 4760 Initialized agent: Triage Specialist
INFO 2025-06-14 11:34:26,386 multi_agent_system 5584 4760 Initialized agent: Diagnostic Specialist
INFO 2025-06-14 11:34:26,388 multi_agent_system 5584 4760 Initialized agent: Treatment Specialist
INFO 2025-06-14 11:34:26,390 multi_agent_system 5584 4760 Initialized agent: Clinical Pharmacist
WARNING 2025-06-14 11:34:27,042 log 5584 4760 Not Found: /api/users/users/99999/
WARNING 2025-06-14 11:34:27,053 log 5584 4760 Bad Request: /api/appointments/appointments/
INFO 2025-06-14 11:37:14,730 multi_agent_system 13392 12856 Initialized agent: Triage Specialist
INFO 2025-06-14 11:37:14,732 multi_agent_system 13392 12856 Initialized agent: Diagnostic Specialist
INFO 2025-06-14 11:37:14,734 multi_agent_system 13392 12856 Initialized agent: Treatment Specialist
INFO 2025-06-14 11:37:14,736 multi_agent_system 13392 12856 Initialized agent: Clinical Pharmacist
WARNING 2025-06-14 11:37:15,438 log 13392 12856 Not Found: /api/users/users/99999/
WARNING 2025-06-14 11:37:15,442 log 13392 12856 Bad Request: /api/appointments/appointments/
INFO 2025-06-14 11:44:11,072 multi_agent_system 6604 18092 Initialized agent: Triage Specialist
INFO 2025-06-14 11:44:11,074 multi_agent_system 6604 18092 Initialized agent: Diagnostic Specialist
INFO 2025-06-14 11:44:11,076 multi_agent_system 6604 18092 Initialized agent: Treatment Specialist
INFO 2025-06-14 11:44:11,077 multi_agent_system 6604 18092 Initialized agent: Clinical Pharmacist
INFO 2025-06-14 11:46:30,550 multi_agent_system 17572 3160 Initialized agent: Triage Specialist
INFO 2025-06-14 11:46:30,552 multi_agent_system 17572 3160 Initialized agent: Diagnostic Specialist
INFO 2025-06-14 11:46:30,555 multi_agent_system 17572 3160 Initialized agent: Treatment Specialist
INFO 2025-06-14 11:46:30,558 multi_agent_system 17572 3160 Initialized agent: Clinical Pharmacist
INFO 2025-06-14 11:46:49,936 multi_agent_system 7444 17608 Initialized agent: Triage Specialist
INFO 2025-06-14 11:46:49,939 multi_agent_system 7444 17608 Initialized agent: Diagnostic Specialist
INFO 2025-06-14 11:46:49,945 multi_agent_system 7444 17608 Initialized agent: Treatment Specialist
INFO 2025-06-14 11:46:49,949 multi_agent_system 7444 17608 Initialized agent: Clinical Pharmacist
INFO 2025-06-14 11:47:15,197 multi_agent_system 13236 13756 Initialized agent: Triage Specialist
INFO 2025-06-14 11:47:15,199 multi_agent_system 13236 13756 Initialized agent: Diagnostic Specialist
INFO 2025-06-14 11:47:15,201 multi_agent_system 13236 13756 Initialized agent: Treatment Specialist
INFO 2025-06-14 11:47:15,203 multi_agent_system 13236 13756 Initialized agent: Clinical Pharmacist
INFO 2025-06-14 11:47:37,767 multi_agent_system 16972 17388 Initialized agent: Triage Specialist
INFO 2025-06-14 11:47:37,770 multi_agent_system 16972 17388 Initialized agent: Diagnostic Specialist
INFO 2025-06-14 11:47:37,773 multi_agent_system 16972 17388 Initialized agent: Treatment Specialist
INFO 2025-06-14 11:47:37,775 multi_agent_system 16972 17388 Initialized agent: Clinical Pharmacist
INFO 2025-06-14 11:48:01,509 multi_agent_system 4056 8780 Initialized agent: Triage Specialist
INFO 2025-06-14 11:48:01,512 multi_agent_system 4056 8780 Initialized agent: Diagnostic Specialist
INFO 2025-06-14 11:48:01,518 multi_agent_system 4056 8780 Initialized agent: Treatment Specialist
INFO 2025-06-14 11:48:01,521 multi_agent_system 4056 8780 Initialized agent: Clinical Pharmacist
WARNING 2025-06-14 11:48:03,646 log 4056 8780 Not Found: /api/users/users/99999/
WARNING 2025-06-14 11:48:03,657 log 4056 8780 Bad Request: /api/appointments/appointments/
INFO 2025-06-14 11:48:51,416 multi_agent_system 11256 15676 Initialized agent: Triage Specialist
INFO 2025-06-14 11:48:51,420 multi_agent_system 11256 15676 Initialized agent: Diagnostic Specialist
INFO 2025-06-14 11:48:51,422 multi_agent_system 11256 15676 Initialized agent: Treatment Specialist
INFO 2025-06-14 11:48:51,424 multi_agent_system 11256 15676 Initialized agent: Clinical Pharmacist
INFO 2025-06-14 11:50:14,968 multi_agent_system 17052 9644 Initialized agent: Triage Specialist
INFO 2025-06-14 11:50:14,970 multi_agent_system 17052 9644 Initialized agent: Diagnostic Specialist
INFO 2025-06-14 11:50:14,973 multi_agent_system 17052 9644 Initialized agent: Treatment Specialist
INFO 2025-06-14 11:50:14,976 multi_agent_system 17052 9644 Initialized agent: Clinical Pharmacist
WARNING 2025-06-14 11:50:15,679 log 17052 9644 Not Found: /api/users/users/99999/
WARNING 2025-06-14 11:50:15,684 log 17052 9644 Bad Request: /api/appointments/appointments/
INFO 2025-06-14 11:50:32,481 multi_agent_system 12220 12820 Initialized agent: Triage Specialist
INFO 2025-06-14 11:50:32,483 multi_agent_system 12220 12820 Initialized agent: Diagnostic Specialist
INFO 2025-06-14 11:50:32,485 multi_agent_system 12220 12820 Initialized agent: Treatment Specialist
INFO 2025-06-14 11:50:32,486 multi_agent_system 12220 12820 Initialized agent: Clinical Pharmacist
INFO 2025-06-14 11:51:46,213 multi_agent_system 10760 5944 Initialized agent: Triage Specialist
INFO 2025-06-14 11:51:46,216 multi_agent_system 10760 5944 Initialized agent: Diagnostic Specialist
INFO 2025-06-14 11:51:46,218 multi_agent_system 10760 5944 Initialized agent: Treatment Specialist
INFO 2025-06-14 11:51:46,220 multi_agent_system 10760 5944 Initialized agent: Clinical Pharmacist
INFO 2025-06-14 11:52:07,220 multi_agent_system 9924 1000 Initialized agent: Triage Specialist
INFO 2025-06-14 11:52:07,222 multi_agent_system 9924 1000 Initialized agent: Diagnostic Specialist
INFO 2025-06-14 11:52:07,224 multi_agent_system 9924 1000 Initialized agent: Treatment Specialist
INFO 2025-06-14 11:52:07,226 multi_agent_system 9924 1000 Initialized agent: Clinical Pharmacist
INFO 2025-06-14 11:52:15,533 multi_agent_system 15048 17060 Initialized agent: Triage Specialist
INFO 2025-06-14 11:52:15,535 multi_agent_system 15048 17060 Initialized agent: Diagnostic Specialist
INFO 2025-06-14 11:52:15,537 multi_agent_system 15048 17060 Initialized agent: Treatment Specialist
INFO 2025-06-14 11:52:15,538 multi_agent_system 15048 17060 Initialized agent: Clinical Pharmacist
INFO 2025-06-14 11:54:34,520 autoreload 3308 14232 Watching for file changes with StatReloader
INFO 2025-06-14 11:54:39,914 multi_agent_system 3308 14232 Initialized agent: Triage Specialist
INFO 2025-06-14 11:54:39,916 multi_agent_system 3308 14232 Initialized agent: Diagnostic Specialist
INFO 2025-06-14 11:54:39,918 multi_agent_system 3308 14232 Initialized agent: Treatment Specialist
INFO 2025-06-14 11:54:39,919 multi_agent_system 3308 14232 Initialized agent: Clinical Pharmacist
INFO 2025-06-14 11:54:58,198 autoreload 3308 14232 C:\Users\<USER>\OneDrive\Desktop\HMS\backend\hms\urls.py changed, reloading.
INFO 2025-06-14 11:56:42,379 multi_agent_system 17480 7684 Initialized agent: Triage Specialist
INFO 2025-06-14 11:56:42,383 multi_agent_system 17480 7684 Initialized agent: Diagnostic Specialist
INFO 2025-06-14 11:56:42,386 multi_agent_system 17480 7684 Initialized agent: Treatment Specialist
INFO 2025-06-14 11:56:42,388 multi_agent_system 17480 7684 Initialized agent: Clinical Pharmacist
INFO 2025-06-14 12:03:15,085 multi_agent_system 12720 13348 Initialized agent: Triage Specialist
INFO 2025-06-14 12:03:15,087 multi_agent_system 12720 13348 Initialized agent: Diagnostic Specialist
INFO 2025-06-14 12:03:15,089 multi_agent_system 12720 13348 Initialized agent: Treatment Specialist
INFO 2025-06-14 12:03:15,092 multi_agent_system 12720 13348 Initialized agent: Clinical Pharmacist
INFO 2025-06-14 12:37:13,176 multi_agent_system 12872 7616 Initialized agent: Triage Specialist
INFO 2025-06-14 12:37:13,180 multi_agent_system 12872 7616 Initialized agent: Diagnostic Specialist
INFO 2025-06-14 12:37:13,182 multi_agent_system 12872 7616 Initialized agent: Treatment Specialist
INFO 2025-06-14 12:37:13,184 multi_agent_system 12872 7616 Initialized agent: Clinical Pharmacist
INFO 2025-06-14 12:54:04,373 multi_agent_system 7036 18380 Initialized agent: Triage Specialist
INFO 2025-06-14 12:54:04,375 multi_agent_system 7036 18380 Initialized agent: Diagnostic Specialist
INFO 2025-06-14 12:54:04,379 multi_agent_system 7036 18380 Initialized agent: Treatment Specialist
INFO 2025-06-14 12:54:04,381 multi_agent_system 7036 18380 Initialized agent: Clinical Pharmacist
INFO 2025-06-14 12:54:28,101 multi_agent_system 6432 17880 Initialized agent: Triage Specialist
INFO 2025-06-14 12:54:28,103 multi_agent_system 6432 17880 Initialized agent: Diagnostic Specialist
INFO 2025-06-14 12:54:28,105 multi_agent_system 6432 17880 Initialized agent: Treatment Specialist
INFO 2025-06-14 12:54:28,106 multi_agent_system 6432 17880 Initialized agent: Clinical Pharmacist
WARNING 2025-06-14 12:54:28,740 log 6432 17880 Bad Request: /api/auth/register/
INFO 2025-06-14 12:55:03,807 multi_agent_system 8220 2416 Initialized agent: Triage Specialist
INFO 2025-06-14 12:55:03,809 multi_agent_system 8220 2416 Initialized agent: Diagnostic Specialist
INFO 2025-06-14 12:55:03,811 multi_agent_system 8220 2416 Initialized agent: Treatment Specialist
INFO 2025-06-14 12:55:03,813 multi_agent_system 8220 2416 Initialized agent: Clinical Pharmacist
INFO 2025-06-14 12:55:28,621 multi_agent_system 3672 12052 Initialized agent: Triage Specialist
INFO 2025-06-14 12:55:28,623 multi_agent_system 3672 12052 Initialized agent: Diagnostic Specialist
INFO 2025-06-14 12:55:28,625 multi_agent_system 3672 12052 Initialized agent: Treatment Specialist
INFO 2025-06-14 12:55:28,626 multi_agent_system 3672 12052 Initialized agent: Clinical Pharmacist
INFO 2025-06-14 12:55:49,911 multi_agent_system 15728 6228 Initialized agent: Triage Specialist
INFO 2025-06-14 12:55:49,913 multi_agent_system 15728 6228 Initialized agent: Diagnostic Specialist
INFO 2025-06-14 12:55:49,915 multi_agent_system 15728 6228 Initialized agent: Treatment Specialist
INFO 2025-06-14 12:55:49,917 multi_agent_system 15728 6228 Initialized agent: Clinical Pharmacist
WARNING 2025-06-14 12:55:50,649 log 15728 6228 Not Found: /api/users/users/99999/
WARNING 2025-06-14 12:55:50,656 log 15728 6228 Bad Request: /api/appointments/appointments/
INFO 2025-06-14 12:57:17,466 multi_agent_system 2996 6720 Initialized agent: Triage Specialist
INFO 2025-06-14 12:57:17,468 multi_agent_system 2996 6720 Initialized agent: Diagnostic Specialist
INFO 2025-06-14 12:57:17,470 multi_agent_system 2996 6720 Initialized agent: Treatment Specialist
INFO 2025-06-14 12:57:17,471 multi_agent_system 2996 6720 Initialized agent: Clinical Pharmacist
INFO 2025-06-14 12:57:27,438 multi_agent_system 3768 8320 Initialized agent: Triage Specialist
INFO 2025-06-14 12:57:27,440 multi_agent_system 3768 8320 Initialized agent: Diagnostic Specialist
INFO 2025-06-14 12:57:27,444 multi_agent_system 3768 8320 Initialized agent: Treatment Specialist
INFO 2025-06-14 12:57:27,446 multi_agent_system 3768 8320 Initialized agent: Clinical Pharmacist
INFO 2025-06-14 12:57:37,784 multi_agent_system 4056 17268 Initialized agent: Triage Specialist
INFO 2025-06-14 12:57:37,786 multi_agent_system 4056 17268 Initialized agent: Diagnostic Specialist
INFO 2025-06-14 12:57:37,788 multi_agent_system 4056 17268 Initialized agent: Treatment Specialist
INFO 2025-06-14 12:57:37,790 multi_agent_system 4056 17268 Initialized agent: Clinical Pharmacist
INFO 2025-06-14 12:57:59,590 multi_agent_system 17264 12932 Initialized agent: Triage Specialist
INFO 2025-06-14 12:57:59,594 multi_agent_system 17264 12932 Initialized agent: Diagnostic Specialist
INFO 2025-06-14 12:57:59,596 multi_agent_system 17264 12932 Initialized agent: Treatment Specialist
INFO 2025-06-14 12:57:59,598 multi_agent_system 17264 12932 Initialized agent: Clinical Pharmacist
WARNING 2025-06-14 12:58:00,316 log 17264 12932 Not Found: /api/users/users/99999/
WARNING 2025-06-14 12:58:00,320 log 17264 12932 Bad Request: /api/appointments/appointments/
INFO 2025-06-14 13:00:38,587 multi_agent_system 16176 3148 Initialized agent: Triage Specialist
INFO 2025-06-14 13:00:38,590 multi_agent_system 16176 3148 Initialized agent: Diagnostic Specialist
INFO 2025-06-14 13:00:38,592 multi_agent_system 16176 3148 Initialized agent: Treatment Specialist
INFO 2025-06-14 13:00:38,595 multi_agent_system 16176 3148 Initialized agent: Clinical Pharmacist
INFO 2025-06-14 13:01:02,332 multi_agent_system 4408 8096 Initialized agent: Triage Specialist
INFO 2025-06-14 13:01:02,334 multi_agent_system 4408 8096 Initialized agent: Diagnostic Specialist
INFO 2025-06-14 13:01:02,336 multi_agent_system 4408 8096 Initialized agent: Treatment Specialist
INFO 2025-06-14 13:01:02,339 multi_agent_system 4408 8096 Initialized agent: Clinical Pharmacist
INFO 2025-06-14 13:01:22,017 multi_agent_system 8892 15408 Initialized agent: Triage Specialist
INFO 2025-06-14 13:01:22,019 multi_agent_system 8892 15408 Initialized agent: Diagnostic Specialist
INFO 2025-06-14 13:01:22,021 multi_agent_system 8892 15408 Initialized agent: Treatment Specialist
INFO 2025-06-14 13:01:22,024 multi_agent_system 8892 15408 Initialized agent: Clinical Pharmacist
WARNING 2025-06-14 13:01:22,647 log 8892 15408 Bad Request: /api/auth/register/
INFO 2025-06-14 13:02:05,596 multi_agent_system 16680 17800 Initialized agent: Triage Specialist
INFO 2025-06-14 13:02:05,599 multi_agent_system 16680 17800 Initialized agent: Diagnostic Specialist
INFO 2025-06-14 13:02:05,600 multi_agent_system 16680 17800 Initialized agent: Treatment Specialist
INFO 2025-06-14 13:02:05,602 multi_agent_system 16680 17800 Initialized agent: Clinical Pharmacist
INFO 2025-06-14 13:02:41,598 multi_agent_system 18124 7584 Initialized agent: Triage Specialist
INFO 2025-06-14 13:02:41,600 multi_agent_system 18124 7584 Initialized agent: Diagnostic Specialist
INFO 2025-06-14 13:02:41,602 multi_agent_system 18124 7584 Initialized agent: Treatment Specialist
INFO 2025-06-14 13:02:41,604 multi_agent_system 18124 7584 Initialized agent: Clinical Pharmacist
INFO 2025-06-14 13:04:27,701 multi_agent_system 8320 15208 Initialized agent: Triage Specialist
INFO 2025-06-14 13:04:27,704 multi_agent_system 8320 15208 Initialized agent: Diagnostic Specialist
INFO 2025-06-14 13:04:27,707 multi_agent_system 8320 15208 Initialized agent: Treatment Specialist
INFO 2025-06-14 13:04:27,711 multi_agent_system 8320 15208 Initialized agent: Clinical Pharmacist
INFO 2025-06-14 13:04:50,256 multi_agent_system 5224 5772 Initialized agent: Triage Specialist
INFO 2025-06-14 13:04:50,259 multi_agent_system 5224 5772 Initialized agent: Diagnostic Specialist
INFO 2025-06-14 13:04:50,261 multi_agent_system 5224 5772 Initialized agent: Treatment Specialist
INFO 2025-06-14 13:04:50,263 multi_agent_system 5224 5772 Initialized agent: Clinical Pharmacist
WARNING 2025-06-14 13:04:50,968 log 5224 5772 Not Found: /api/users/users/99999/
WARNING 2025-06-14 13:04:50,974 log 5224 5772 Bad Request: /api/appointments/appointments/
INFO 2025-06-14 13:05:10,570 multi_agent_system 15452 15512 Initialized agent: Triage Specialist
INFO 2025-06-14 13:05:10,574 multi_agent_system 15452 15512 Initialized agent: Diagnostic Specialist
INFO 2025-06-14 13:05:10,576 multi_agent_system 15452 15512 Initialized agent: Treatment Specialist
INFO 2025-06-14 13:05:10,578 multi_agent_system 15452 15512 Initialized agent: Clinical Pharmacist
INFO 2025-06-14 13:06:12,737 multi_agent_system 12904 12920 Initialized agent: Triage Specialist
INFO 2025-06-14 13:06:12,739 multi_agent_system 12904 12920 Initialized agent: Diagnostic Specialist
INFO 2025-06-14 13:06:12,741 multi_agent_system 12904 12920 Initialized agent: Treatment Specialist
INFO 2025-06-14 13:06:12,744 multi_agent_system 12904 12920 Initialized agent: Clinical Pharmacist
INFO 2025-06-14 13:06:33,771 multi_agent_system 10232 13616 Initialized agent: Triage Specialist
INFO 2025-06-14 13:06:33,774 multi_agent_system 10232 13616 Initialized agent: Diagnostic Specialist
INFO 2025-06-14 13:06:33,777 multi_agent_system 10232 13616 Initialized agent: Treatment Specialist
INFO 2025-06-14 13:06:33,780 multi_agent_system 10232 13616 Initialized agent: Clinical Pharmacist
INFO 2025-06-14 13:06:55,051 multi_agent_system 8268 9440 Initialized agent: Triage Specialist
INFO 2025-06-14 13:06:55,053 multi_agent_system 8268 9440 Initialized agent: Diagnostic Specialist
INFO 2025-06-14 13:06:55,055 multi_agent_system 8268 9440 Initialized agent: Treatment Specialist
INFO 2025-06-14 13:06:55,056 multi_agent_system 8268 9440 Initialized agent: Clinical Pharmacist
INFO 2025-06-14 13:07:21,758 multi_agent_system 16392 18292 Initialized agent: Triage Specialist
INFO 2025-06-14 13:07:21,760 multi_agent_system 16392 18292 Initialized agent: Diagnostic Specialist
INFO 2025-06-14 13:07:21,762 multi_agent_system 16392 18292 Initialized agent: Treatment Specialist
INFO 2025-06-14 13:07:21,766 multi_agent_system 16392 18292 Initialized agent: Clinical Pharmacist
INFO 2025-06-14 13:07:41,541 multi_agent_system 11080 8164 Initialized agent: Triage Specialist
INFO 2025-06-14 13:07:41,543 multi_agent_system 11080 8164 Initialized agent: Diagnostic Specialist
INFO 2025-06-14 13:07:41,545 multi_agent_system 11080 8164 Initialized agent: Treatment Specialist
INFO 2025-06-14 13:07:41,547 multi_agent_system 11080 8164 Initialized agent: Clinical Pharmacist
WARNING 2025-06-14 13:07:42,149 log 11080 8164 Bad Request: /api/auth/register/
INFO 2025-06-14 13:08:54,781 multi_agent_system 16092 2180 Initialized agent: Triage Specialist
INFO 2025-06-14 13:08:54,783 multi_agent_system 16092 2180 Initialized agent: Diagnostic Specialist
INFO 2025-06-14 13:08:54,785 multi_agent_system 16092 2180 Initialized agent: Treatment Specialist
INFO 2025-06-14 13:08:54,789 multi_agent_system 16092 2180 Initialized agent: Clinical Pharmacist
INFO 2025-06-14 13:11:11,542 multi_agent_system 7740 9844 Initialized agent: Triage Specialist
INFO 2025-06-14 13:11:11,544 multi_agent_system 7740 9844 Initialized agent: Diagnostic Specialist
INFO 2025-06-14 13:11:11,548 multi_agent_system 7740 9844 Initialized agent: Treatment Specialist
INFO 2025-06-14 13:11:11,550 multi_agent_system 7740 9844 Initialized agent: Clinical Pharmacist
INFO 2025-06-14 13:20:50,007 multi_agent_system 16472 15512 Initialized agent: Triage Specialist
INFO 2025-06-14 13:20:50,009 multi_agent_system 16472 15512 Initialized agent: Diagnostic Specialist
INFO 2025-06-14 13:20:50,011 multi_agent_system 16472 15512 Initialized agent: Treatment Specialist
INFO 2025-06-14 13:20:50,012 multi_agent_system 16472 15512 Initialized agent: Clinical Pharmacist
INFO 2025-06-14 13:21:57,811 multi_agent_system 996 7016 Initialized agent: Triage Specialist
INFO 2025-06-14 13:21:57,813 multi_agent_system 996 7016 Initialized agent: Diagnostic Specialist
INFO 2025-06-14 13:21:57,815 multi_agent_system 996 7016 Initialized agent: Treatment Specialist
INFO 2025-06-14 13:21:57,817 multi_agent_system 996 7016 Initialized agent: Clinical Pharmacist
INFO 2025-06-14 13:23:04,386 multi_agent_system 15672 3184 Initialized agent: Triage Specialist
INFO 2025-06-14 13:23:04,388 multi_agent_system 15672 3184 Initialized agent: Diagnostic Specialist
INFO 2025-06-14 13:23:04,390 multi_agent_system 15672 3184 Initialized agent: Treatment Specialist
INFO 2025-06-14 13:23:04,392 multi_agent_system 15672 3184 Initialized agent: Clinical Pharmacist
WARNING 2025-06-14 13:23:05,090 log 15672 3184 Not Found: /api/users/users/99999/
WARNING 2025-06-14 13:23:05,097 log 15672 3184 Bad Request: /api/appointments/appointments/
INFO 2025-06-14 16:39:44,054 autoreload 13744 11364 Watching for file changes with StatReloader
INFO 2025-06-14 16:39:52,806 multi_agent_system 13744 11364 Initialized agent: Triage Specialist
INFO 2025-06-14 16:39:52,811 multi_agent_system 13744 11364 Initialized agent: Diagnostic Specialist
INFO 2025-06-14 16:39:52,815 multi_agent_system 13744 11364 Initialized agent: Treatment Specialist
INFO 2025-06-14 16:39:52,818 multi_agent_system 13744 11364 Initialized agent: Clinical Pharmacist
WARNING 2025-06-14 16:40:24,906 log 13744 14528 Unauthorized: /api/
WARNING 2025-06-14 16:40:24,907 basehttp 13744 14528 "GET /api/ HTTP/1.1" 401 169
WARNING 2025-06-14 16:40:36,142 log 13744 9320 Unauthorized: /api/
WARNING 2025-06-14 16:40:36,143 basehttp 13744 9320 "GET /api/ HTTP/1.1" 401 169
WARNING 2025-06-14 16:40:43,252 log 13744 1320 Unauthorized: /api/
WARNING 2025-06-14 16:40:43,252 basehttp 13744 1320 "GET /api/ HTTP/1.1" 401 169
INFO 2025-06-15 09:41:18,460 autoreload 13636 8008 Watching for file changes with StatReloader
INFO 2025-06-15 09:41:34,962 multi_agent_system 13636 8008 Initialized agent: Triage Specialist
INFO 2025-06-15 09:41:34,970 multi_agent_system 13636 8008 Initialized agent: Diagnostic Specialist
INFO 2025-06-15 09:41:34,974 multi_agent_system 13636 8008 Initialized agent: Treatment Specialist
INFO 2025-06-15 09:41:34,978 multi_agent_system 13636 8008 Initialized agent: Clinical Pharmacist
INFO 2025-06-15 09:47:52,560 autoreload 13636 8008 C:\Users\<USER>\OneDrive\Desktop\HMS\backend\appointment_system\views.py changed, reloading.
INFO 2025-06-15 09:47:57,544 autoreload 6340 14976 Watching for file changes with StatReloader
INFO 2025-06-15 09:48:05,057 multi_agent_system 6340 14976 Initialized agent: Triage Specialist
INFO 2025-06-15 09:48:05,062 multi_agent_system 6340 14976 Initialized agent: Diagnostic Specialist
INFO 2025-06-15 09:48:05,065 multi_agent_system 6340 14976 Initialized agent: Treatment Specialist
INFO 2025-06-15 09:48:05,067 multi_agent_system 6340 14976 Initialized agent: Clinical Pharmacist
INFO 2025-06-15 09:48:17,417 autoreload 6340 14976 C:\Users\<USER>\OneDrive\Desktop\HMS\backend\appointment_system\views.py changed, reloading.
INFO 2025-06-15 09:48:22,912 autoreload 7392 12368 Watching for file changes with StatReloader
INFO 2025-06-15 09:48:30,614 multi_agent_system 7392 12368 Initialized agent: Triage Specialist
INFO 2025-06-15 09:48:30,617 multi_agent_system 7392 12368 Initialized agent: Diagnostic Specialist
INFO 2025-06-15 09:48:30,619 multi_agent_system 7392 12368 Initialized agent: Treatment Specialist
INFO 2025-06-15 09:48:30,622 multi_agent_system 7392 12368 Initialized agent: Clinical Pharmacist
INFO 2025-06-15 09:48:36,607 autoreload 7392 12368 C:\Users\<USER>\OneDrive\Desktop\HMS\backend\appointment_system\views.py changed, reloading.
INFO 2025-06-15 09:48:40,415 autoreload 20256 15572 Watching for file changes with StatReloader
INFO 2025-06-15 09:48:45,901 multi_agent_system 20256 15572 Initialized agent: Triage Specialist
INFO 2025-06-15 09:48:45,905 multi_agent_system 20256 15572 Initialized agent: Diagnostic Specialist
INFO 2025-06-15 09:48:45,907 multi_agent_system 20256 15572 Initialized agent: Treatment Specialist
INFO 2025-06-15 09:48:45,911 multi_agent_system 20256 15572 Initialized agent: Clinical Pharmacist
INFO 2025-06-15 09:48:53,756 autoreload 20256 15572 C:\Users\<USER>\OneDrive\Desktop\HMS\backend\patient_management\views.py changed, reloading.
INFO 2025-06-15 09:48:58,653 autoreload 8528 22016 Watching for file changes with StatReloader
INFO 2025-06-15 09:49:24,097 autoreload 8528 22016 C:\Users\<USER>\OneDrive\Desktop\HMS\backend\patient_management\views.py changed, reloading.
INFO 2025-06-15 09:49:25,440 autoreload 12024 17240 Watching for file changes with StatReloader
INFO 2025-06-15 10:17:41,434 autoreload 12024 17240 C:\Users\<USER>\OneDrive\Desktop\HMS\backend\authentication\views.py changed, reloading.
INFO 2025-06-15 10:17:43,589 autoreload 16844 9260 Watching for file changes with StatReloader
INFO 2025-06-15 10:18:02,554 autoreload 16844 9260 C:\Users\<USER>\OneDrive\Desktop\HMS\backend\authentication\views.py changed, reloading.
INFO 2025-06-15 10:18:04,104 autoreload 15900 5388 Watching for file changes with StatReloader
INFO 2025-06-15 10:32:26,211 autoreload 7656 11504 Watching for file changes with StatReloader
INFO 2025-06-15 10:32:56,450 autoreload 7656 11504 C:\Users\<USER>\OneDrive\Desktop\HMS\backend\patient_management\views.py changed, reloading.
INFO 2025-06-15 10:32:56,680 autoreload 15900 5388 C:\Users\<USER>\OneDrive\Desktop\HMS\backend\patient_management\views.py changed, reloading.
INFO 2025-06-15 10:32:59,080 autoreload 7336 3216 Watching for file changes with StatReloader
INFO 2025-06-15 10:32:59,427 autoreload 4744 3696 Watching for file changes with StatReloader
INFO 2025-06-15 10:33:27,964 autoreload 14704 17616 Watching for file changes with StatReloader
INFO 2025-06-15 10:33:37,786 multi_agent_system 4744 3696 Initialized agent: Triage Specialist
INFO 2025-06-15 10:33:37,794 multi_agent_system 4744 3696 Initialized agent: Diagnostic Specialist
INFO 2025-06-15 10:33:37,799 multi_agent_system 4744 3696 Initialized agent: Treatment Specialist
INFO 2025-06-15 10:33:37,803 multi_agent_system 4744 3696 Initialized agent: Clinical Pharmacist
INFO 2025-06-15 10:33:38,680 multi_agent_system 14704 17616 Initialized agent: Triage Specialist
INFO 2025-06-15 10:33:38,683 multi_agent_system 14704 17616 Initialized agent: Diagnostic Specialist
INFO 2025-06-15 10:33:38,686 multi_agent_system 14704 17616 Initialized agent: Treatment Specialist
INFO 2025-06-15 10:33:38,689 multi_agent_system 14704 17616 Initialized agent: Clinical Pharmacist
INFO 2025-06-15 10:34:03,528 autoreload 4744 3696 C:\Users\<USER>\OneDrive\Desktop\HMS\backend\appointment_system\views.py changed, reloading.
INFO 2025-06-15 10:34:03,918 autoreload 14704 17616 C:\Users\<USER>\OneDrive\Desktop\HMS\backend\appointment_system\views.py changed, reloading.
INFO 2025-06-15 10:34:07,348 autoreload 6452 18484 Watching for file changes with StatReloader
INFO 2025-06-15 10:34:07,661 autoreload 15540 19924 Watching for file changes with StatReloader
INFO 2025-06-15 10:34:13,438 multi_agent_system 6452 18484 Initialized agent: Triage Specialist
INFO 2025-06-15 10:34:13,444 multi_agent_system 6452 18484 Initialized agent: Diagnostic Specialist
INFO 2025-06-15 10:34:13,455 multi_agent_system 6452 18484 Initialized agent: Treatment Specialist
INFO 2025-06-15 10:34:13,463 multi_agent_system 6452 18484 Initialized agent: Clinical Pharmacist
INFO 2025-06-15 10:34:34,548 basehttp 6452 14280 "OPTIONS /api/ HTTP/1.1" 200 0
INFO 2025-06-15 10:34:34,549 basehttp 6452 11904 "OPTIONS /api/auth/ HTTP/1.1" 200 0
INFO 2025-06-15 10:34:34,551 basehttp 6452 10616 "OPTIONS /api/ai/health/status/ HTTP/1.1" 200 0
WARNING 2025-06-15 10:34:34,570 log 6452 10616 Unauthorized: /api/
WARNING 2025-06-15 10:34:34,571 log 6452 14280 Unauthorized: /api/ai/health/status/
WARNING 2025-06-15 10:34:34,571 basehttp 6452 10616 "GET /api/ HTTP/1.1" 401 169
WARNING 2025-06-15 10:34:34,571 basehttp 6452 14280 "GET /api/ai/health/status/ HTTP/1.1" 401 169
WARNING 2025-06-15 10:34:34,605 log 6452 11904 Not Found: /api/auth/
WARNING 2025-06-15 10:34:34,605 basehttp 6452 11904 "OPTIONS /api/auth/ HTTP/1.1" 404 5770
INFO 2025-06-15 10:35:21,982 basehttp 6452 11904 "OPTIONS /api/auth/login/ HTTP/1.1" 200 0
INFO 2025-06-15 10:35:22,626 basehttp 6452 11904 "POST /api/auth/login/ HTTP/1.1" 200 804
INFO 2025-06-15 10:52:30,686 basehttp 6452 16004 "OPTIONS /api/ HTTP/1.1" 200 0
INFO 2025-06-15 10:52:30,696 basehttp 6452 17324 "OPTIONS /api/auth/ HTTP/1.1" 200 0
INFO 2025-06-15 10:52:30,705 basehttp 6452 10808 "OPTIONS /api/ai/health/status/ HTTP/1.1" 200 0
WARNING 2025-06-15 10:52:30,962 log 6452 10808 Unauthorized: /api/ai/health/status/
WARNING 2025-06-15 10:52:31,010 log 6452 16004 Unauthorized: /api/
WARNING 2025-06-15 10:52:31,040 basehttp 6452 10808 "GET /api/ai/health/status/ HTTP/1.1" 401 169
WARNING 2025-06-15 10:52:31,074 basehttp 6452 16004 "GET /api/ HTTP/1.1" 401 169
WARNING 2025-06-15 10:52:31,121 log 6452 17324 Not Found: /api/auth/
WARNING 2025-06-15 10:52:31,122 basehttp 6452 17324 "OPTIONS /api/auth/ HTTP/1.1" 404 5770
WARNING 2025-06-15 10:52:39,228 log 6452 16004 Unauthorized: /api/
WARNING 2025-06-15 10:52:39,230 log 6452 10808 Unauthorized: /api/ai/health/status/
WARNING 2025-06-15 10:52:39,232 basehttp 6452 10808 "GET /api/ai/health/status/ HTTP/1.1" 401 169
WARNING 2025-06-15 10:52:39,243 log 6452 17324 Not Found: /api/auth/
WARNING 2025-06-15 10:52:39,244 basehttp 6452 16004 "GET /api/ HTTP/1.1" 401 169
WARNING 2025-06-15 10:52:39,244 basehttp 6452 17324 "OPTIONS /api/auth/ HTTP/1.1" 404 5770
WARNING 2025-06-15 10:52:47,597 log 6452 16004 Unauthorized: /api/
WARNING 2025-06-15 10:52:47,606 log 6452 17324 Not Found: /api/auth/
WARNING 2025-06-15 10:52:47,606 log 6452 10808 Unauthorized: /api/ai/health/status/
WARNING 2025-06-15 10:52:47,607 basehttp 6452 17324 "OPTIONS /api/auth/ HTTP/1.1" 404 5770
WARNING 2025-06-15 10:52:47,607 basehttp 6452 16004 "GET /api/ HTTP/1.1" 401 169
WARNING 2025-06-15 10:52:47,608 basehttp 6452 10808 "GET /api/ai/health/status/ HTTP/1.1" 401 169
WARNING 2025-06-15 10:52:48,253 log 6452 16004 Unauthorized: /api/ai/health/status/
WARNING 2025-06-15 10:52:48,259 log 6452 10808 Unauthorized: /api/
WARNING 2025-06-15 10:52:48,263 log 6452 17324 Not Found: /api/auth/
WARNING 2025-06-15 10:52:48,264 basehttp 6452 16004 "GET /api/ai/health/status/ HTTP/1.1" 401 169
WARNING 2025-06-15 10:52:48,268 basehttp 6452 17324 "OPTIONS /api/auth/ HTTP/1.1" 404 5770
WARNING 2025-06-15 10:52:48,268 basehttp 6452 10808 "GET /api/ HTTP/1.1" 401 169
INFO 2025-06-15 10:53:40,432 basehttp 6452 13252 "POST /api/auth/login/ HTTP/1.1" 200 804
INFO 2025-06-15 10:53:48,061 basehttp 6452 13252 "OPTIONS /api/users/users/ HTTP/1.1" 200 0
INFO 2025-06-15 10:53:48,069 basehttp 6452 11444 "OPTIONS /api/users/users/ HTTP/1.1" 200 0
INFO 2025-06-15 10:53:48,130 basehttp 6452 13252 "GET /api/users/users/ HTTP/1.1" 200 10432
INFO 2025-06-15 10:53:48,148 basehttp 6452 13252 "GET /api/users/users/ HTTP/1.1" 200 10432
INFO 2025-06-15 10:53:55,403 basehttp 6452 13252 "OPTIONS /api/patients/?page=1&page_size=10 HTTP/1.1" 200 0
INFO 2025-06-15 10:53:55,403 basehttp 6452 11444 "OPTIONS /api/patients/?page=1&page_size=10 HTTP/1.1" 200 0
INFO 2025-06-15 10:53:55,675 basehttp 6452 11444 "GET /api/patients/?page=1&page_size=10 HTTP/1.1" 200 335
INFO 2025-06-15 10:53:55,683 basehttp 6452 11444 "GET /api/patients/?page=1&page_size=10 HTTP/1.1" 200 335
INFO 2025-06-15 11:00:21,499 basehttp 6452 12976 "OPTIONS /api/ai/conversations/chat/ HTTP/1.1" 200 0
ERROR 2025-06-15 11:00:22,615 ai_core 6452 12976 Gemini AI error: Invalid argument provided to Gemini: 400 API key expired. Please renew the API key. [reason: "API_KEY_INVALID"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "generativelanguage.googleapis.com"
}
, locale: "en-US"
message: "API key expired. Please renew the API key."
]
ERROR 2025-06-15 11:00:22,616 log 6452 12976 Internal Server Error: /api/ai/conversations/chat/
ERROR 2025-06-15 11:00:22,616 basehttp 6452 12976 "POST /api/ai/conversations/chat/ HTTP/1.1" 500 332
INFO 2025-06-15 11:31:21,203 basehttp 6452 20396 "OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
INFO 2025-06-15 11:31:21,237 basehttp 6452 14668 "OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
INFO 2025-06-15 11:31:21,296 basehttp 6452 20396 "GET /api/auth/profile/ HTTP/1.1" 200 273
INFO 2025-06-15 11:31:21,309 basehttp 6452 20396 "GET /api/auth/profile/ HTTP/1.1" 200 273
WARNING 2025-06-15 11:31:23,261 log 6452 14668 Unauthorized: /api/
WARNING 2025-06-15 11:31:23,266 basehttp 6452 14668 "GET /api/ HTTP/1.1" 401 169
WARNING 2025-06-15 11:31:23,267 log 6452 9072 Unauthorized: /api/ai/health/status/
WARNING 2025-06-15 11:31:23,268 basehttp 6452 9072 "GET /api/ai/health/status/ HTTP/1.1" 401 169
WARNING 2025-06-15 11:31:23,300 log 6452 20396 Not Found: /api/auth/
WARNING 2025-06-15 11:31:23,300 basehttp 6452 20396 "OPTIONS /api/auth/ HTTP/1.1" 404 5770
INFO 2025-06-15 11:32:40,833 basehttp 6452 20396 "OPTIONS /api/ HTTP/1.1" 200 0
INFO 2025-06-15 11:32:40,834 basehttp 6452 9072 "OPTIONS /api/auth/ HTTP/1.1" 200 0
INFO 2025-06-15 11:32:40,835 basehttp 6452 14668 "OPTIONS /api/ai/health/status/ HTTP/1.1" 200 0
WARNING 2025-06-15 11:32:40,848 log 6452 9072 Unauthorized: /api/
WARNING 2025-06-15 11:32:40,849 log 6452 20396 Unauthorized: /api/ai/health/status/
WARNING 2025-06-15 11:32:40,870 basehttp 6452 9072 "GET /api/ HTTP/1.1" 401 169
WARNING 2025-06-15 11:32:40,871 basehttp 6452 20396 "GET /api/ai/health/status/ HTTP/1.1" 401 169
WARNING 2025-06-15 11:32:40,872 log 6452 14668 Not Found: /api/auth/
WARNING 2025-06-15 11:32:40,873 basehttp 6452 14668 "OPTIONS /api/auth/ HTTP/1.1" 404 5770
WARNING 2025-06-15 11:36:25,016 log 6452 20396 Unauthorized: /api/
WARNING 2025-06-15 11:36:25,017 log 6452 9072 Unauthorized: /api/ai/health/status/
WARNING 2025-06-15 11:36:25,018 basehttp 6452 20396 "GET /api/ HTTP/1.1" 401 169
WARNING 2025-06-15 11:36:25,035 basehttp 6452 9072 "GET /api/ai/health/status/ HTTP/1.1" 401 169
WARNING 2025-06-15 11:36:25,036 log 6452 14668 Not Found: /api/auth/
WARNING 2025-06-15 11:36:25,040 basehttp 6452 14668 "OPTIONS /api/auth/ HTTP/1.1" 404 5770
WARNING 2025-06-15 11:42:29,071 log 6452 18964 Not Found: /api/auth/
WARNING 2025-06-15 11:42:29,073 log 6452 3836 Unauthorized: /api/
WARNING 2025-06-15 11:42:29,074 basehttp 6452 18964 "OPTIONS /api/auth/ HTTP/1.1" 404 5770
WARNING 2025-06-15 11:42:29,077 basehttp 6452 3836 "GET /api/ HTTP/1.1" 401 169
WARNING 2025-06-15 11:42:29,078 log 6452 19732 Unauthorized: /api/ai/health/status/
WARNING 2025-06-15 11:42:29,080 basehttp 6452 19732 "GET /api/ai/health/status/ HTTP/1.1" 401 169
INFO 2025-06-15 11:44:11,097 basehttp 6452 19732 "OPTIONS /api/auth/login/ HTTP/1.1" 200 0
INFO 2025-06-15 11:44:11,710 basehttp 6452 19732 "POST /api/auth/login/ HTTP/1.1" 200 872
WARNING 2025-06-15 11:44:53,075 log 6452 3836 Unauthorized: /api/
WARNING 2025-06-15 11:44:53,079 log 6452 18964 Unauthorized: /api/ai/health/status/
WARNING 2025-06-15 11:44:53,089 log 6452 19732 Not Found: /api/auth/
WARNING 2025-06-15 11:44:53,091 basehttp 6452 3836 "GET /api/ HTTP/1.1" 401 169
WARNING 2025-06-15 11:44:53,092 basehttp 6452 18964 "GET /api/ai/health/status/ HTTP/1.1" 401 169
WARNING 2025-06-15 11:44:53,092 basehttp 6452 19732 "OPTIONS /api/auth/ HTTP/1.1" 404 5770
INFO 2025-06-15 11:49:47,455 basehttp 6452 19732 "OPTIONS /api/appointments/ HTTP/1.1" 200 0
WARNING 2025-06-15 11:49:47,468 log 6452 19732 Method Not Allowed: /api/appointments/
WARNING 2025-06-15 11:49:47,468 basehttp 6452 19732 "POST /api/appointments/ HTTP/1.1" 405 141
INFO 2025-06-15 12:07:26,801 basehttp 6452 16716 "OPTIONS /api/auth/logout/ HTTP/1.1" 200 0
WARNING 2025-06-15 12:07:26,822 log 6452 16716 Bad Request: /api/auth/logout/
WARNING 2025-06-15 12:07:26,822 basehttp 6452 16716 "POST /api/auth/logout/ HTTP/1.1" 400 25
INFO 2025-06-15 12:07:33,099 basehttp 6452 16716 "POST /api/auth/login/ HTTP/1.1" 200 829
INFO 2025-06-15 12:08:47,126 basehttp 6452 16716 "OPTIONS /api/ HTTP/1.1" 200 0
WARNING 2025-06-15 12:08:47,133 log 6452 16716 Unauthorized: /api/
INFO 2025-06-15 12:08:47,137 basehttp 6452 14584 "OPTIONS /api/auth/ HTTP/1.1" 200 0
INFO 2025-06-15 12:08:47,139 basehttp 6452 18868 "OPTIONS /api/ai/health/status/ HTTP/1.1" 200 0
WARNING 2025-06-15 12:08:47,140 basehttp 6452 16716 "GET /api/ HTTP/1.1" 401 169
WARNING 2025-06-15 12:08:47,149 log 6452 16716 Unauthorized: /api/ai/health/status/
WARNING 2025-06-15 12:08:47,172 log 6452 14584 Not Found: /api/auth/
WARNING 2025-06-15 12:08:47,173 basehttp 6452 16716 "GET /api/ai/health/status/ HTTP/1.1" 401 169
WARNING 2025-06-15 12:08:47,176 basehttp 6452 14584 "OPTIONS /api/auth/ HTTP/1.1" 404 5770
INFO 2025-06-15 12:09:10,742 basehttp 6452 14584 "OPTIONS /api/auth/login/ HTTP/1.1" 200 0
INFO 2025-06-15 12:09:11,794 basehttp 6452 14584 "POST /api/auth/login/ HTTP/1.1" 200 838
INFO 2025-06-15 12:09:16,758 basehttp 6452 16716 "OPTIONS /api/patients/?page=1&page_size=10 HTTP/1.1" 200 0
INFO 2025-06-15 12:09:16,758 basehttp 6452 14584 "OPTIONS /api/patients/?page=1&page_size=10 HTTP/1.1" 200 0
INFO 2025-06-15 12:09:16,776 basehttp 6452 14584 "GET /api/patients/?page=1&page_size=10 HTTP/1.1" 200 335
INFO 2025-06-15 12:09:16,787 basehttp 6452 14584 "GET /api/patients/?page=1&page_size=10 HTTP/1.1" 200 335
INFO 2025-06-15 12:09:27,751 basehttp 6452 14584 "OPTIONS /api/billing/invoices/ HTTP/1.1" 200 0
INFO 2025-06-15 12:09:27,751 basehttp 6452 16716 "OPTIONS /api/billing/invoices/ HTTP/1.1" 200 0
INFO 2025-06-15 12:09:28,025 basehttp 6452 16716 "GET /api/billing/invoices/ HTTP/1.1" 200 2383
INFO 2025-06-15 12:09:28,045 basehttp 6452 16716 "GET /api/billing/invoices/ HTTP/1.1" 200 2383
WARNING 2025-06-15 12:11:15,073 log 6452 14584 Unauthorized: /api/
WARNING 2025-06-15 12:11:15,089 log 6452 16716 Not Found: /api/auth/
WARNING 2025-06-15 12:11:15,089 log 6452 18868 Unauthorized: /api/ai/health/status/
WARNING 2025-06-15 12:11:15,089 basehttp 6452 14584 "GET /api/ HTTP/1.1" 401 169
WARNING 2025-06-15 12:11:15,091 basehttp 6452 16716 "OPTIONS /api/auth/ HTTP/1.1" 404 5770
WARNING 2025-06-15 12:11:15,092 basehttp 6452 18868 "GET /api/ai/health/status/ HTTP/1.1" 401 169
INFO 2025-06-15 12:15:04,480 basehttp 6452 16716 "GET /api/patients/?page=1&page_size=10 HTTP/1.1" 200 335
INFO 2025-06-15 12:15:04,490 basehttp 6452 16716 "GET /api/patients/?page=1&page_size=10 HTTP/1.1" 200 335
INFO 2025-06-15 12:15:10,371 basehttp 6452 16716 "GET /api/billing/invoices/ HTTP/1.1" 200 2383
INFO 2025-06-15 12:15:10,403 basehttp 6452 16716 "GET /api/billing/invoices/ HTTP/1.1" 200 2383
INFO 2025-06-15 12:15:17,213 basehttp 6452 16716 "GET /api/patients/?page=1&page_size=10 HTTP/1.1" 200 335
INFO 2025-06-15 12:15:17,222 basehttp 6452 16716 "GET /api/patients/?page=1&page_size=10 HTTP/1.1" 200 335
INFO 2025-06-15 12:15:26,553 basehttp 6452 16716 "GET /api/billing/invoices/ HTTP/1.1" 200 2383
INFO 2025-06-15 12:15:26,573 basehttp 6452 16716 "GET /api/billing/invoices/ HTTP/1.1" 200 2383
INFO 2025-06-15 12:15:28,853 basehttp 6452 16716 "OPTIONS /api/auth/logout/ HTTP/1.1" 200 0
WARNING 2025-06-15 12:15:28,857 log 6452 16716 Bad Request: /api/auth/logout/
WARNING 2025-06-15 12:15:28,857 basehttp 6452 16716 "POST /api/auth/logout/ HTTP/1.1" 400 25
INFO 2025-06-15 12:15:38,207 basehttp 6452 16716 "POST /api/auth/login/ HTTP/1.1" 200 872
WARNING 2025-06-15 12:21:09,527 log 6452 11496 Unauthorized: /api/
WARNING 2025-06-15 12:21:09,543 log 6452 6024 Unauthorized: /api/ai/health/status/
WARNING 2025-06-15 12:21:09,552 basehttp 6452 11496 "GET /api/ HTTP/1.1" 401 169
WARNING 2025-06-15 12:21:09,556 log 6452 21864 Not Found: /api/auth/
WARNING 2025-06-15 12:21:09,557 basehttp 6452 6024 "GET /api/ai/health/status/ HTTP/1.1" 401 169
WARNING 2025-06-15 12:21:09,558 basehttp 6452 21864 "OPTIONS /api/auth/ HTTP/1.1" 404 5770
WARNING 2025-06-15 12:36:43,260 log 6452 8388 Unauthorized: /api/
WARNING 2025-06-15 12:36:43,271 log 6452 4176 Not Found: /api/auth/
WARNING 2025-06-15 12:36:43,272 basehttp 6452 8388 "GET /api/ HTTP/1.1" 401 169
WARNING 2025-06-15 12:36:43,285 log 6452 19936 Not Found: /api/auth/
WARNING 2025-06-15 12:36:43,286 basehttp 6452 4176 "OPTIONS /api/auth/ HTTP/1.1" 404 5770
WARNING 2025-06-15 12:36:43,286 log 6452 4048 Unauthorized: /api/ai/health/status/
WARNING 2025-06-15 12:36:43,288 basehttp 6452 19936 "OPTIONS /api/auth/ HTTP/1.1" 404 5770
WARNING 2025-06-15 12:36:43,289 log 6452 3748 Unauthorized: /api/
WARNING 2025-06-15 12:36:43,290 basehttp 6452 4048 "GET /api/ai/health/status/ HTTP/1.1" 401 169
WARNING 2025-06-15 12:36:43,292 log 6452 19936 Unauthorized: /api/ai/health/status/
WARNING 2025-06-15 12:36:43,292 basehttp 6452 3748 "GET /api/ HTTP/1.1" 401 169
WARNING 2025-06-15 12:36:43,292 basehttp 6452 19936 "GET /api/ai/health/status/ HTTP/1.1" 401 169
WARNING 2025-06-15 12:57:08,606 log 6452 13668 Unauthorized: /api/
WARNING 2025-06-15 12:57:08,607 basehttp 6452 13668 "GET /api/ HTTP/1.1" 401 169
WARNING 2025-06-15 12:57:08,634 log 6452 17968 Not Found: /api/auth/
WARNING 2025-06-15 12:57:08,634 log 6452 14304 Unauthorized: /api/ai/health/status/
WARNING 2025-06-15 12:57:08,637 basehttp 6452 14304 "GET /api/ai/health/status/ HTTP/1.1" 401 169
WARNING 2025-06-15 12:57:08,639 basehttp 6452 17968 "OPTIONS /api/auth/ HTTP/1.1" 404 5770
INFO 2025-06-15 12:57:09,254 basehttp 6452 17968 "OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
INFO 2025-06-15 12:57:09,254 basehttp 6452 14304 "OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
INFO 2025-06-15 12:57:09,321 basehttp 6452 14304 "GET /api/auth/profile/ HTTP/1.1" 200 338
INFO 2025-06-15 12:57:09,339 basehttp 6452 14304 "GET /api/auth/profile/ HTTP/1.1" 200 338
WARNING 2025-06-15 12:57:11,142 log 6452 14304 Not Found: /api/auth/
WARNING 2025-06-15 12:57:11,143 log 6452 17968 Unauthorized: /api/
WARNING 2025-06-15 12:57:11,145 basehttp 6452 14304 "OPTIONS /api/auth/ HTTP/1.1" 404 5770
WARNING 2025-06-15 12:57:11,146 log 6452 13668 Unauthorized: /api/ai/health/status/
WARNING 2025-06-15 12:57:11,146 basehttp 6452 17968 "GET /api/ HTTP/1.1" 401 169
WARNING 2025-06-15 12:57:11,147 basehttp 6452 13668 "GET /api/ai/health/status/ HTTP/1.1" 401 169
WARNING 2025-06-15 12:57:12,930 log 6452 13668 Unauthorized: /api/auth/profile/
WARNING 2025-06-15 12:57:12,931 basehttp 6452 13668 "GET /api/auth/profile/ HTTP/1.1" 401 278
WARNING 2025-06-15 12:57:12,936 log 6452 13668 Unauthorized: /api/auth/profile/
WARNING 2025-06-15 12:57:12,937 basehttp 6452 13668 "GET /api/auth/profile/ HTTP/1.1" 401 278
INFO 2025-06-15 12:57:13,001 basehttp 6452 13668 "OPTIONS /api/auth/token/refresh/ HTTP/1.1" 200 0
INFO 2025-06-15 12:57:13,002 basehttp 6452 14304 "OPTIONS /api/auth/token/refresh/ HTTP/1.1" 200 0
INFO 2025-06-15 12:57:13,032 basehttp 6452 13668 "OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
INFO 2025-06-15 12:57:13,103 basehttp 6452 14940 "OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
INFO 2025-06-15 12:57:13,112 basehttp 6452 13668 "GET /api/auth/profile/ HTTP/1.1" 200 295
INFO 2025-06-15 12:57:13,151 basehttp 6452 13668 "GET /api/auth/profile/ HTTP/1.1" 200 295
ERROR 2025-06-15 12:57:13,363 log 6452 14304 Internal Server Error: /api/auth/token/refresh/
ERROR 2025-06-15 12:57:13,365 basehttp 6452 14304 "POST /api/auth/token/refresh/ HTTP/1.1" 500 162
ERROR 2025-06-15 12:57:13,386 log 6452 17968 Internal Server Error: /api/auth/token/refresh/
ERROR 2025-06-15 12:57:13,407 basehttp 6452 17968 "POST /api/auth/token/refresh/ HTTP/1.1" 500 162
INFO 2025-06-15 12:57:15,228 basehttp 6452 17968 "OPTIONS /api/auth/ HTTP/1.1" 200 0
WARNING 2025-06-15 12:57:15,236 log 6452 14304 Not Found: /api/auth/
WARNING 2025-06-15 12:57:15,237 log 6452 13668 Unauthorized: /api/
WARNING 2025-06-15 12:57:15,243 log 6452 14940 Not Found: /api/auth/
INFO 2025-06-15 12:57:15,244 basehttp 6452 17968 "OPTIONS /api/ HTTP/1.1" 200 0
WARNING 2025-06-15 12:57:15,247 basehttp 6452 14304 "OPTIONS /api/auth/ HTTP/1.1" 404 5770
WARNING 2025-06-15 12:57:15,250 log 6452 18572 Not Found: /api/auth/
WARNING 2025-06-15 12:57:15,251 basehttp 6452 13668 "GET /api/ HTTP/1.1" 401 169
WARNING 2025-06-15 12:57:15,253 basehttp 6452 14940 "OPTIONS /api/auth/ HTTP/1.1" 404 5770
INFO 2025-06-15 12:57:15,253 basehttp 6452 4780 "OPTIONS /api/ HTTP/1.1" 200 0
INFO 2025-06-15 12:57:15,253 basehttp 6452 17968 "OPTIONS /api/auth/ HTTP/1.1" 200 0
WARNING 2025-06-15 12:57:15,255 log 6452 14940 Unauthorized: /api/ai/health/status/
WARNING 2025-06-15 12:57:15,256 basehttp 6452 18572 "OPTIONS /api/auth/ HTTP/1.1" 404 5770
WARNING 2025-06-15 12:57:15,263 log 6452 14304 Not Found: /api/auth/
INFO 2025-06-15 12:57:15,264 basehttp 6452 13668 "OPTIONS /api/ai/health/status/ HTTP/1.1" 200 0
INFO 2025-06-15 12:57:15,264 basehttp 6452 4780 "OPTIONS /api/ai/health/status/ HTTP/1.1" 200 0
WARNING 2025-06-15 12:57:15,266 log 6452 18572 Unauthorized: /api/
WARNING 2025-06-15 12:57:15,273 log 6452 17968 Not Found: /api/auth/
WARNING 2025-06-15 12:57:15,274 basehttp 6452 14940 "GET /api/ai/health/status/ HTTP/1.1" 401 169
WARNING 2025-06-15 12:57:15,274 basehttp 6452 14304 "OPTIONS /api/auth/ HTTP/1.1" 404 5770
WARNING 2025-06-15 12:57:15,281 log 6452 13668 Not Found: /api/auth/
WARNING 2025-06-15 12:57:15,282 log 6452 14940 Unauthorized: /api/ai/health/status/
WARNING 2025-06-15 12:57:15,282 basehttp 6452 18572 "GET /api/ HTTP/1.1" 401 169
WARNING 2025-06-15 12:57:15,283 basehttp 6452 17968 "OPTIONS /api/auth/ HTTP/1.1" 404 5770
WARNING 2025-06-15 12:57:15,285 basehttp 6452 13668 "OPTIONS /api/auth/ HTTP/1.1" 404 5770
WARNING 2025-06-15 12:57:15,286 log 6452 18572 Unauthorized: /api/
WARNING 2025-06-15 12:57:15,286 basehttp 6452 14940 "GET /api/ai/health/status/ HTTP/1.1" 401 169
WARNING 2025-06-15 12:57:15,288 log 6452 14940 Unauthorized: /api/ai/health/status/
WARNING 2025-06-15 12:57:15,289 basehttp 6452 18572 "GET /api/ HTTP/1.1" 401 169
WARNING 2025-06-15 12:57:15,289 basehttp 6452 14940 "GET /api/ai/health/status/ HTTP/1.1" 401 169
WARNING 2025-06-15 12:57:15,292 log 6452 18572 Unauthorized: /api/
WARNING 2025-06-15 12:57:15,293 log 6452 14940 Unauthorized: /api/ai/health/status/
WARNING 2025-06-15 12:57:15,294 basehttp 6452 18572 "GET /api/ HTTP/1.1" 401 169
WARNING 2025-06-15 12:57:15,294 basehttp 6452 14940 "GET /api/ai/health/status/ HTTP/1.1" 401 169
WARNING 2025-06-15 12:57:15,298 log 6452 18572 Unauthorized: /api/
WARNING 2025-06-15 12:57:15,298 log 6452 14940 Unauthorized: /api/ai/health/status/
WARNING 2025-06-15 12:57:15,299 basehttp 6452 18572 "GET /api/ HTTP/1.1" 401 169
WARNING 2025-06-15 12:57:15,299 basehttp 6452 14940 "GET /api/ai/health/status/ HTTP/1.1" 401 169
WARNING 2025-06-15 12:57:15,304 log 6452 14940 Unauthorized: /api/ai/health/status/
WARNING 2025-06-15 12:57:15,305 log 6452 18572 Unauthorized: /api/
WARNING 2025-06-15 12:57:15,306 basehttp 6452 14940 "GET /api/ai/health/status/ HTTP/1.1" 401 169
WARNING 2025-06-15 12:57:15,306 basehttp 6452 18572 "GET /api/ HTTP/1.1" 401 169
INFO 2025-06-15 12:57:17,220 basehttp 6452 14940 "OPTIONS /api/ HTTP/1.1" 200 0
INFO 2025-06-15 12:57:17,220 basehttp 6452 13668 "OPTIONS /api/ai/health/status/ HTTP/1.1" 200 0
INFO 2025-06-15 12:57:17,220 basehttp 6452 18572 "OPTIONS /api/auth/ HTTP/1.1" 200 0
WARNING 2025-06-15 12:57:17,224 log 6452 13668 Unauthorized: /api/
WARNING 2025-06-15 12:57:17,225 log 6452 14940 Unauthorized: /api/ai/health/status/
WARNING 2025-06-15 12:57:17,225 basehttp 6452 13668 "GET /api/ HTTP/1.1" 401 169
WARNING 2025-06-15 12:57:17,229 log 6452 18572 Not Found: /api/auth/
WARNING 2025-06-15 12:57:17,230 basehttp 6452 14940 "GET /api/ai/health/status/ HTTP/1.1" 401 169
WARNING 2025-06-15 12:57:17,230 basehttp 6452 18572 "OPTIONS /api/auth/ HTTP/1.1" 404 5770
WARNING 2025-06-15 13:10:59,180 log 6452 22444 Unauthorized: /api/
WARNING 2025-06-15 13:10:59,208 log 6452 7296 Unauthorized: /api/ai/health/status/
WARNING 2025-06-15 13:10:59,226 log 6452 10468 Not Found: /api/auth/
WARNING 2025-06-15 13:10:59,228 basehttp 6452 7296 "GET /api/ai/health/status/ HTTP/1.1" 401 169
WARNING 2025-06-15 13:10:59,230 basehttp 6452 22444 "GET /api/ HTTP/1.1" 401 169
WARNING 2025-06-15 13:10:59,234 basehttp 6452 10468 "OPTIONS /api/auth/ HTTP/1.1" 404 5770
INFO 2025-06-15 15:36:35,821 basehttp 6452 1988 "OPTIONS /api/ HTTP/1.1" 200 0
INFO 2025-06-15 15:36:35,825 basehttp 6452 14232 "OPTIONS /api/auth/ HTTP/1.1" 200 0
INFO 2025-06-15 15:36:35,828 basehttp 6452 22104 "OPTIONS /api/ai/health/status/ HTTP/1.1" 200 0
WARNING 2025-06-15 15:36:35,841 log 6452 14232 Unauthorized: /api/
WARNING 2025-06-15 15:36:35,842 log 6452 22104 Unauthorized: /api/ai/health/status/
WARNING 2025-06-15 15:36:35,843 basehttp 6452 14232 "GET /api/ HTTP/1.1" 401 169
WARNING 2025-06-15 15:36:35,844 basehttp 6452 22104 "GET /api/ai/health/status/ HTTP/1.1" 401 169
WARNING 2025-06-15 15:36:35,879 log 6452 1988 Not Found: /api/auth/
WARNING 2025-06-15 15:36:35,880 basehttp 6452 1988 "OPTIONS /api/auth/ HTTP/1.1" 404 5770
WARNING 2025-06-15 15:39:29,213 log 6452 22104 Unauthorized: /api/
WARNING 2025-06-15 15:39:29,250 log 6452 1988 Not Found: /api/auth/
WARNING 2025-06-15 15:39:29,251 log 6452 14232 Unauthorized: /api/ai/health/status/
WARNING 2025-06-15 15:39:29,252 basehttp 6452 22104 "GET /api/ HTTP/1.1" 401 169
WARNING 2025-06-15 15:39:29,253 basehttp 6452 1988 "OPTIONS /api/auth/ HTTP/1.1" 404 5770
WARNING 2025-06-15 15:39:29,255 basehttp 6452 14232 "GET /api/ai/health/status/ HTTP/1.1" 401 169
INFO 2025-06-15 15:39:35,844 basehttp 6452 14232 "OPTIONS /api/auth/login/ HTTP/1.1" 200 0
INFO 2025-06-15 15:39:37,003 basehttp 6452 14232 "POST /api/auth/login/ HTTP/1.1" 200 804
INFO 2025-06-15 15:39:41,097 basehttp 6452 14232 "OPTIONS /api/ai/conversations/ HTTP/1.1" 200 0
INFO 2025-06-15 15:39:41,098 basehttp 6452 1988 "OPTIONS /api/ai/conversations/ HTTP/1.1" 200 0
INFO 2025-06-15 15:39:41,114 basehttp 6452 22104 "GET /api/ai/health/status/ HTTP/1.1" 200 170
INFO 2025-06-15 15:39:41,148 basehttp 6452 22104 "GET /api/ai/health/status/ HTTP/1.1" 200 170
INFO 2025-06-15 15:39:41,183 basehttp 6452 1988 "GET /api/ai/conversations/ HTTP/1.1" 200 690
INFO 2025-06-15 15:39:41,196 basehttp 6452 1988 "GET /api/ai/conversations/ HTTP/1.1" 200 690
INFO 2025-06-15 15:39:43,498 basehttp 6452 1988 "OPTIONS /api/users/users/ HTTP/1.1" 200 0
INFO 2025-06-15 15:39:43,498 basehttp 6452 22104 "OPTIONS /api/users/users/ HTTP/1.1" 200 0
INFO 2025-06-15 15:39:43,547 basehttp 6452 22104 "GET /api/users/users/ HTTP/1.1" 200 10432
INFO 2025-06-15 15:39:43,562 basehttp 6452 1988 "GET /api/users/users/ HTTP/1.1" 200 10432
INFO 2025-06-15 15:39:46,240 basehttp 6452 1988 "OPTIONS /api/patients/?page=1&page_size=10 HTTP/1.1" 200 0
INFO 2025-06-15 15:39:46,240 basehttp 6452 22104 "OPTIONS /api/patients/?page=1&page_size=10 HTTP/1.1" 200 0
INFO 2025-06-15 15:39:46,252 basehttp 6452 22104 "GET /api/patients/?page=1&page_size=10 HTTP/1.1" 200 335
INFO 2025-06-15 15:39:46,262 basehttp 6452 22104 "GET /api/patients/?page=1&page_size=10 HTTP/1.1" 200 335
INFO 2025-06-15 15:49:33,694 basehttp 6452 15764 "GET /api/ai/health/status/ HTTP/1.1" 200 170
INFO 2025-06-15 15:49:33,721 basehttp 6452 11768 "GET /api/ai/conversations/ HTTP/1.1" 200 690
INFO 2025-06-15 15:49:33,722 basehttp 6452 15764 "GET /api/ai/health/status/ HTTP/1.1" 200 170
INFO 2025-06-15 15:49:33,742 basehttp 6452 11768 "GET /api/ai/conversations/ HTTP/1.1" 200 690
INFO 2025-06-15 15:49:48,032 basehttp 6452 11768 "GET /api/users/users/ HTTP/1.1" 200 10432
INFO 2025-06-15 15:49:48,051 basehttp 6452 15764 "GET /api/users/users/ HTTP/1.1" 200 10432
INFO 2025-06-15 15:49:48,916 basehttp 6452 15764 "GET /api/patients/?page=1&page_size=10 HTTP/1.1" 200 335
INFO 2025-06-15 15:49:48,928 basehttp 6452 15764 "GET /api/patients/?page=1&page_size=10 HTTP/1.1" 200 335
INFO 2025-06-15 15:50:00,458 basehttp 6452 15764 "GET /api/ai/health/status/ HTTP/1.1" 200 170
INFO 2025-06-15 15:50:00,477 basehttp 6452 11768 "GET /api/ai/conversations/ HTTP/1.1" 200 690
INFO 2025-06-15 15:50:00,480 basehttp 6452 15764 "GET /api/ai/health/status/ HTTP/1.1" 200 170
INFO 2025-06-15 15:50:00,508 basehttp 6452 11768 "GET /api/ai/conversations/ HTTP/1.1" 200 690
WARNING 2025-06-15 15:52:45,447 log 6452 9388 Unauthorized: /api/
WARNING 2025-06-15 15:52:45,485 log 6452 16948 Not Found: /api/auth/
WARNING 2025-06-15 15:52:45,486 log 6452 15640 Unauthorized: /api/ai/health/status/
WARNING 2025-06-15 15:52:45,488 basehttp 6452 15640 "GET /api/ai/health/status/ HTTP/1.1" 401 169
WARNING 2025-06-15 15:52:45,487 basehttp 6452 16948 "OPTIONS /api/auth/ HTTP/1.1" 404 5770
WARNING 2025-06-15 15:52:45,487 basehttp 6452 9388 "GET /api/ HTTP/1.1" 401 169
INFO 2025-06-15 15:57:45,573 basehttp 6452 16948 "GET /api/ai/health/status/ HTTP/1.1" 200 170
INFO 2025-06-15 15:57:45,604 basehttp 6452 15640 "GET /api/ai/conversations/ HTTP/1.1" 200 690
INFO 2025-06-15 15:57:45,608 basehttp 6452 16948 "GET /api/ai/health/status/ HTTP/1.1" 200 170
INFO 2025-06-15 15:57:45,631 basehttp 6452 15640 "GET /api/ai/conversations/ HTTP/1.1" 200 690
INFO 2025-06-15 15:57:46,815 basehttp 6452 15640 "GET /api/users/users/ HTTP/1.1" 200 10432
INFO 2025-06-15 15:57:46,843 basehttp 6452 15640 "GET /api/users/users/ HTTP/1.1" 200 10432
INFO 2025-06-15 15:57:48,548 basehttp 6452 15640 "GET /api/patients/?page=1&page_size=10 HTTP/1.1" 200 335
INFO 2025-06-15 15:57:48,560 basehttp 6452 15640 "GET /api/patients/?page=1&page_size=10 HTTP/1.1" 200 335
INFO 2025-06-15 15:57:54,819 basehttp 6452 16948 "OPTIONS /api/staff/staff-profiles/ HTTP/1.1" 200 0
INFO 2025-06-15 15:57:54,819 basehttp 6452 15640 "OPTIONS /api/staff/staff-profiles/ HTTP/1.1" 200 0
INFO 2025-06-15 15:57:54,916 basehttp 6452 15640 "GET /api/staff/staff-profiles/ HTTP/1.1" 200 1516
INFO 2025-06-15 15:57:54,933 basehttp 6452 15640 "GET /api/staff/staff-profiles/ HTTP/1.1" 200 1516
INFO 2025-06-15 16:02:37,174 basehttp 6452 15640 "OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
INFO 2025-06-15 16:02:37,176 basehttp 6452 16948 "OPTIONS /api/auth/profile/ HTTP/1.1" 200 0
INFO 2025-06-15 16:02:37,205 basehttp 6452 16948 "GET /api/auth/profile/ HTTP/1.1" 200 273
INFO 2025-06-15 16:02:37,218 basehttp 6452 16948 "GET /api/auth/profile/ HTTP/1.1" 200 273
WARNING 2025-06-15 16:02:39,139 log 6452 16948 Unauthorized: /api/
WARNING 2025-06-15 16:02:39,159 log 6452 15640 Not Found: /api/auth/
WARNING 2025-06-15 16:02:39,163 basehttp 6452 16948 "GET /api/ HTTP/1.1" 401 169
WARNING 2025-06-15 16:02:39,164 basehttp 6452 15640 "OPTIONS /api/auth/ HTTP/1.1" 404 5770
WARNING 2025-06-15 16:02:39,165 log 6452 15312 Unauthorized: /api/ai/health/status/
WARNING 2025-06-15 16:02:39,168 basehttp 6452 15312 "GET /api/ai/health/status/ HTTP/1.1" 401 169
WARNING 2025-06-15 16:09:44,157 log 6452 4852 Unauthorized: /api/
WARNING 2025-06-15 16:09:44,191 log 6452 20756 Unauthorized: /api/ai/health/status/
WARNING 2025-06-15 16:09:44,217 log 6452 10148 Not Found: /api/auth/
WARNING 2025-06-15 16:09:44,219 basehttp 6452 4852 "GET /api/ HTTP/1.1" 401 169
WARNING 2025-06-15 16:09:44,220 basehttp 6452 20756 "GET /api/ai/health/status/ HTTP/1.1" 401 169
WARNING 2025-06-15 16:09:44,221 basehttp 6452 10148 "OPTIONS /api/auth/ HTTP/1.1" 404 5770
INFO 2025-06-15 16:10:14,984 basehttp 6452 10148 "GET /api/auth/profile/ HTTP/1.1" 200 273
INFO 2025-06-15 16:10:15,005 basehttp 6452 10148 "GET /api/auth/profile/ HTTP/1.1" 200 273
INFO 2025-06-15 16:10:16,205 basehttp 6452 10148 "GET /api/users/users/ HTTP/1.1" 200 10432
INFO 2025-06-15 16:10:16,236 basehttp 6452 10148 "GET /api/users/users/ HTTP/1.1" 200 10432
INFO 2025-06-15 16:10:16,759 basehttp 6452 10148 "GET /api/patients/?page=1&page_size=10 HTTP/1.1" 200 335
INFO 2025-06-15 16:10:16,772 basehttp 6452 10148 "GET /api/patients/?page=1&page_size=10 HTTP/1.1" 200 335
WARNING 2025-06-15 16:10:16,806 log 6452 20756 Unauthorized: /api/
WARNING 2025-06-15 16:10:16,811 log 6452 10148 Not Found: /api/auth/
WARNING 2025-06-15 16:10:16,811 log 6452 4852 Unauthorized: /api/ai/health/status/
WARNING 2025-06-15 16:10:16,813 basehttp 6452 20756 "GET /api/ HTTP/1.1" 401 169
WARNING 2025-06-15 16:10:16,815 basehttp 6452 10148 "OPTIONS /api/auth/ HTTP/1.1" 404 5770
WARNING 2025-06-15 16:10:16,817 basehttp 6452 4852 "GET /api/ai/health/status/ HTTP/1.1" 401 169
INFO 2025-06-15 16:10:19,133 basehttp 6452 4852 "GET /api/staff/staff-profiles/ HTTP/1.1" 200 1516
INFO 2025-06-15 16:10:19,156 basehttp 6452 4852 "GET /api/staff/staff-profiles/ HTTP/1.1" 200 1516
INFO 2025-06-15 16:10:23,646 basehttp 6452 4852 "GET /api/auth/profile/ HTTP/1.1" 200 273
INFO 2025-06-15 16:10:23,673 basehttp 6452 4852 "GET /api/auth/profile/ HTTP/1.1" 200 273
WARNING 2025-06-15 16:10:25,582 log 6452 10148 Unauthorized: /api/
WARNING 2025-06-15 16:10:25,625 log 6452 20756 Unauthorized: /api/ai/health/status/
WARNING 2025-06-15 16:10:25,625 basehttp 6452 10148 "GET /api/ HTTP/1.1" 401 169
WARNING 2025-06-15 16:10:25,629 log 6452 4852 Not Found: /api/auth/
WARNING 2025-06-15 16:10:25,630 basehttp 6452 20756 "GET /api/ai/health/status/ HTTP/1.1" 401 169
WARNING 2025-06-15 16:10:25,638 basehttp 6452 4852 "OPTIONS /api/auth/ HTTP/1.1" 404 5770
INFO 2025-06-15 16:10:25,771 basehttp 6452 4852 "GET /api/staff/staff-profiles/ HTTP/1.1" 200 1516
INFO 2025-06-15 16:10:25,797 basehttp 6452 4852 "GET /api/staff/staff-profiles/ HTTP/1.1" 200 1516
INFO 2025-06-15 16:11:41,326 basehttp 6452 4852 "OPTIONS /api/inventory/items/ HTTP/1.1" 200 0
INFO 2025-06-15 16:11:41,326 basehttp 6452 20756 "OPTIONS /api/inventory/items/ HTTP/1.1" 200 0
INFO 2025-06-15 16:11:41,434 basehttp 6452 20756 "GET /api/inventory/items/ HTTP/1.1" 200 2082
INFO 2025-06-15 16:11:41,465 basehttp 6452 20756 "GET /api/inventory/items/ HTTP/1.1" 200 2082
INFO 2025-06-15 16:12:44,186 basehttp 6452 20756 "GET /api/ai/health/status/ HTTP/1.1" 200 170
INFO 2025-06-15 16:12:44,203 basehttp 6452 20756 "GET /api/ai/health/status/ HTTP/1.1" 200 170
INFO 2025-06-15 16:12:44,209 basehttp 6452 4852 "GET /api/ai/conversations/ HTTP/1.1" 200 690
INFO 2025-06-15 16:12:44,236 basehttp 6452 4852 "GET /api/ai/conversations/ HTTP/1.1" 200 690
INFO 2025-06-15 16:12:45,877 basehttp 6452 4852 "GET /api/users/users/ HTTP/1.1" 200 10432
INFO 2025-06-15 16:12:45,897 basehttp 6452 20756 "GET /api/users/users/ HTTP/1.1" 200 10432
INFO 2025-06-15 16:13:12,545 basehttp 6452 20756 "GET /api/patients/?page=1&page_size=10 HTTP/1.1" 200 335
INFO 2025-06-15 16:13:12,553 basehttp 6452 20756 "GET /api/patients/?page=1&page_size=10 HTTP/1.1" 200 335
INFO 2025-06-15 16:15:19,028 basehttp 6452 20756 "GET /api/users/users/ HTTP/1.1" 200 10432
INFO 2025-06-15 16:15:19,054 basehttp 6452 4852 "GET /api/users/users/ HTTP/1.1" 200 10432
INFO 2025-06-15 16:15:20,641 basehttp 6452 4852 "GET /api/staff/staff-profiles/ HTTP/1.1" 200 1516
INFO 2025-06-15 16:15:20,661 basehttp 6452 4852 "GET /api/staff/staff-profiles/ HTTP/1.1" 200 1516
INFO 2025-06-15 16:15:29,188 basehttp 6452 4852 "GET /api/inventory/items/ HTTP/1.1" 200 2082
INFO 2025-06-15 16:15:29,202 basehttp 6452 4852 "GET /api/inventory/items/ HTTP/1.1" 200 2082
INFO 2025-06-15 16:17:28,403 basehttp 6452 4852 "OPTIONS /api/billing/invoices/ HTTP/1.1" 200 0
INFO 2025-06-15 16:17:28,404 basehttp 6452 20756 "OPTIONS /api/billing/invoices/ HTTP/1.1" 200 0
INFO 2025-06-15 16:17:28,543 basehttp 6452 20756 "GET /api/billing/invoices/ HTTP/1.1" 200 2383
INFO 2025-06-15 16:17:28,583 basehttp 6452 20756 "GET /api/billing/invoices/ HTTP/1.1" 200 2383
INFO 2025-06-15 16:17:29,979 basehttp 6452 20756 "GET /api/inventory/items/ HTTP/1.1" 200 2082
INFO 2025-06-15 16:17:29,997 basehttp 6452 20756 "GET /api/inventory/items/ HTTP/1.1" 200 2082
INFO 2025-06-15 16:20:22,000 basehttp 6452 20756 "GET /api/inventory/items/ HTTP/1.1" 200 2082
INFO 2025-06-15 16:20:22,021 basehttp 6452 20756 "GET /api/inventory/items/ HTTP/1.1" 200 2082
INFO 2025-06-15 16:21:27,295 basehttp 6452 20756 "GET /api/billing/invoices/ HTTP/1.1" 200 2383
INFO 2025-06-15 16:21:27,343 basehttp 6452 20756 "GET /api/billing/invoices/ HTTP/1.1" 200 2383
INFO 2025-06-15 16:21:33,648 basehttp 6452 20756 "GET /api/billing/invoices/ HTTP/1.1" 200 2383
INFO 2025-06-15 16:21:33,669 basehttp 6452 20756 "GET /api/billing/invoices/ HTTP/1.1" 200 2383
INFO 2025-06-15 16:21:34,637 basehttp 6452 20756 "GET /api/inventory/items/ HTTP/1.1" 200 2082
INFO 2025-06-15 16:21:34,664 basehttp 6452 20756 "GET /api/inventory/items/ HTTP/1.1" 200 2082
INFO 2025-07-12 17:47:07,287 autoreload 19612 21808 Watching for file changes with StatReloader
INFO 2025-07-12 17:47:53,928 multi_agent_system 19612 21808 Initialized agent: Triage Specialist
INFO 2025-07-12 17:47:53,937 multi_agent_system 19612 21808 Initialized agent: Diagnostic Specialist
INFO 2025-07-12 17:47:53,943 multi_agent_system 19612 21808 Initialized agent: Treatment Specialist
INFO 2025-07-12 17:47:53,954 multi_agent_system 19612 21808 Initialized agent: Clinical Pharmacist
INFO 2025-07-12 17:49:38,763 autoreload 20212 9344 Watching for file changes with StatReloader
WARNING 2025-07-12 17:49:41,394 ai_core 20212 9344 Gemini AI dependencies not installed
WARNING 2025-07-12 17:49:41,398 ai_core 20212 9344 LangGraph dependencies not installed
WARNING 2025-07-12 17:49:41,399 ai_core 20212 9344 ML dependencies not installed
WARNING 2025-07-12 17:49:41,400 ai_core 20212 9344 Gemini AI dependencies not available
WARNING 2025-07-12 17:49:41,400 ai_core 20212 9344 Gemini AI dependencies not available
INFO 2025-07-12 17:49:41,435 multi_agent_system 20212 9344 Initialized agent: Triage Specialist
INFO 2025-07-12 17:49:41,437 multi_agent_system 20212 9344 Initialized agent: Diagnostic Specialist
INFO 2025-07-12 17:49:41,447 multi_agent_system 20212 9344 Initialized agent: Treatment Specialist
INFO 2025-07-12 17:49:41,448 multi_agent_system 20212 9344 Initialized agent: Clinical Pharmacist
WARNING 2025-07-12 17:50:52,653 log 19612 10256 Not Found: /
WARNING 2025-07-12 17:50:52,654 basehttp 19612 10256 "GET / HTTP/1.1" 404 3792
WARNING 2025-07-12 17:50:52,805 log 19612 10256 Not Found: /favicon.ico
WARNING 2025-07-12 17:50:52,806 basehttp 19612 10256 "GET /favicon.ico HTTP/1.1" 404 3843
WARNING 2025-07-12 18:02:57,822 log 19612 21348 Unauthorized: /api/
WARNING 2025-07-12 18:02:57,824 basehttp 19612 21348 "GET /api/ HTTP/1.1" 401 9979
INFO 2025-07-12 18:02:57,902 basehttp 19612 9512 "GET /static/rest_framework/css/prettify.css HTTP/1.1" 200 817
INFO 2025-07-12 18:02:57,905 basehttp 19612 21348 "GET /static/rest_framework/css/bootstrap.min.css HTTP/1.1" 200 121457
INFO 2025-07-12 18:02:57,909 basehttp 19612 20872 "GET /static/rest_framework/css/default.css HTTP/1.1" 200 1152
INFO 2025-07-12 18:02:57,912 basehttp 19612 12468 "GET /static/rest_framework/css/bootstrap-tweaks.css HTTP/1.1" 200 3426
INFO 2025-07-12 18:02:57,951 basehttp 19612 15692 "GET /static/rest_framework/js/ajax-form.js HTTP/1.1" 200 3796
INFO 2025-07-12 18:02:57,951 basehttp 19612 12468 "GET /static/rest_framework/js/default.js HTTP/1.1" 200 1268
INFO 2025-07-12 18:02:57,965 basehttp 19612 9512 "GET /static/rest_framework/js/csrf.js HTTP/1.1" 200 1793
INFO 2025-07-12 18:02:57,967 basehttp 19612 12468 "GET /static/rest_framework/img/grid.png HTTP/1.1" 200 1458
INFO 2025-07-12 18:02:57,982 basehttp 19612 15692 "GET /static/rest_framework/js/load-ajax-form.js HTTP/1.1" 200 59
INFO 2025-07-12 18:02:57,986 basehttp 19612 20872 "GET /static/rest_framework/js/prettify-min.js HTTP/1.1" 200 13632
INFO 2025-07-12 18:02:57,993 basehttp 19612 21348 "GET /static/rest_framework/js/bootstrap.min.js HTTP/1.1" 200 39680
INFO 2025-07-12 18:02:58,018 basehttp 19612 17092 "GET /static/rest_framework/js/jquery-3.7.1.min.js HTTP/1.1" 200 87533
WARNING 2025-07-12 18:02:58,127 log 19612 17092 Not Found: /favicon.ico
WARNING 2025-07-12 18:02:58,128 basehttp 19612 17092 "GET /favicon.ico HTTP/1.1" 404 3843
WARNING 2025-07-12 18:04:34,092 log 19612 21748 Unauthorized: /api/
WARNING 2025-07-12 18:04:34,093 basehttp 19612 21748 "GET /api/ HTTP/1.1" 401 9979
INFO 2025-07-12 18:04:34,117 basehttp 19612 17620 "GET /static/rest_framework/css/bootstrap-tweaks.css HTTP/1.1" 200 3426
INFO 2025-07-12 18:04:34,118 basehttp 19612 20760 "GET /static/rest_framework/css/prettify.css HTTP/1.1" 200 817
INFO 2025-07-12 18:04:34,124 basehttp 19612 12264 "GET /static/rest_framework/css/default.css HTTP/1.1" 200 1152
INFO 2025-07-12 18:04:34,126 basehttp 19612 17620 "GET /static/rest_framework/js/csrf.js HTTP/1.1" 200 1793
INFO 2025-07-12 18:04:34,135 basehttp 19612 12584 "GET /static/rest_framework/js/ajax-form.js HTTP/1.1" 200 3796
INFO 2025-07-12 18:04:34,136 basehttp 19612 21748 "GET /static/rest_framework/css/bootstrap.min.css HTTP/1.1" 200 121457
INFO 2025-07-12 18:04:34,140 basehttp 19612 20760 "GET /static/rest_framework/js/bootstrap.min.js HTTP/1.1" 200 39680
INFO 2025-07-12 18:04:34,141 basehttp 19612 12264 "GET /static/rest_framework/js/default.js HTTP/1.1" 200 1268
INFO 2025-07-12 18:04:34,142 basehttp 19612 17620 "GET /static/rest_framework/js/prettify-min.js HTTP/1.1" 200 13632
INFO 2025-07-12 18:04:34,142 basehttp 19612 12584 "GET /static/rest_framework/js/load-ajax-form.js HTTP/1.1" 200 59
INFO 2025-07-12 18:04:34,143 basehttp 19612 18616 "GET /static/rest_framework/js/jquery-3.7.1.min.js HTTP/1.1" 200 87533
INFO 2025-07-12 18:04:34,221 basehttp 19612 18616 "GET /static/rest_framework/img/grid.png HTTP/1.1" 200 1458
WARNING 2025-07-12 18:04:34,401 log 19612 18616 Not Found: /favicon.ico
WARNING 2025-07-12 18:04:34,401 basehttp 19612 18616 "GET /favicon.ico HTTP/1.1" 404 3843
WARNING 2025-07-12 18:04:43,409 log 19612 18616 Not Found: /api/auth/
WARNING 2025-07-12 18:04:43,409 basehttp 19612 18616 "GET /api/auth/ HTTP/1.1" 404 4636
WARNING 2025-07-12 18:04:53,700 log 19612 18616 Unauthorized: /api/auth/register/
WARNING 2025-07-12 18:04:53,700 basehttp 19612 18616 "GET /api/auth/register/ HTTP/1.1" 401 8835
WARNING 2025-07-12 18:05:44,759 log 19612 18616 Method Not Allowed: /api/auth/login/
WARNING 2025-07-12 18:05:44,760 basehttp 19612 18616 "GET /api/auth/login/ HTTP/1.1" 405 10663
INFO 2025-07-12 18:06:17,405 basehttp 19612 18616 "POST /api/auth/login/ HTTP/1.1" 200 11781
WARNING 2025-07-12 18:06:33,734 log 19612 18616 Unauthorized: /api/patients/patients/
WARNING 2025-07-12 18:06:33,734 basehttp 19612 18616 "GET /api/patients/patients/ HTTP/1.1" 401 10308
WARNING 2025-07-12 18:09:15,959 log 19612 18616 Unauthorized: /api/
WARNING 2025-07-12 18:09:15,960 basehttp 19612 18616 "GET /api/ HTTP/1.1" 401 9979
WARNING 2025-07-12 18:17:14,175 log 19612 20508 Method Not Allowed: /api/auth/login/
WARNING 2025-07-12 18:17:14,176 basehttp 19612 20508 "GET /api/auth/login/ HTTP/1.1" 405 10663
WARNING 2025-07-12 18:18:35,453 log 19612 23516 Method Not Allowed: /api/auth/login/
WARNING 2025-07-12 18:18:35,458 basehttp 19612 23516 "GET /api/auth/login/ HTTP/1.1" 405 10663
WARNING 2025-07-12 18:18:35,767 log 19612 23516 Not Found: /favicon.ico
WARNING 2025-07-12 18:18:35,768 basehttp 19612 23516 "GET /favicon.ico HTTP/1.1" 404 3843
INFO 2025-07-12 18:19:06,292 basehttp 19612 23516 "POST /api/auth/login/ HTTP/1.1" 200 11667
WARNING 2025-07-12 18:19:20,988 log 19612 23516 Unauthorized: /api/patients/patients/
WARNING 2025-07-12 18:19:20,989 basehttp 19612 23516 "GET /api/patients/patients/ HTTP/1.1" 401 10308
WARNING 2025-07-12 18:19:33,187 log 19612 23516 Unauthorized: /api/appointments/
WARNING 2025-07-12 18:19:33,188 basehttp 19612 23516 "GET /api/appointments/ HTTP/1.1" 401 10147
INFO 2025-07-12 18:20:42,107 basehttp 19612 18492 "POST /api/auth/login/ HTTP/1.1" 200 804
INFO 2025-07-12 18:20:43,043 basehttp 19612 14636 "POST /api/auth/login/ HTTP/1.1" 200 824
INFO 2025-07-12 18:20:43,521 basehttp 19612 19104 "POST /api/auth/login/ HTTP/1.1" 200 872
INFO 2025-07-12 18:20:44,067 basehttp 19612 8136 "POST /api/auth/login/ HTTP/1.1" 200 829
INFO 2025-07-12 18:20:44,723 basehttp 19612 22832 "POST /api/auth/login/ HTTP/1.1" 200 838
INFO 2025-07-12 18:20:44,776 basehttp 19612 7752 "GET /api/auth/profile/ HTTP/1.1" 200 273
INFO 2025-07-12 18:20:44,802 basehttp 19612 9516 "GET /api/auth/profile/ HTTP/1.1" 200 290
INFO 2025-07-12 18:20:44,830 basehttp 19612 13548 "GET /api/auth/profile/ HTTP/1.1" 200 338
INFO 2025-07-12 18:20:44,880 basehttp 19612 23448 "GET /api/auth/profile/ HTTP/1.1" 200 295
INFO 2025-07-12 18:20:44,928 basehttp 19612 17128 "GET /api/auth/profile/ HTTP/1.1" 200 304
INFO 2025-07-12 18:20:45,216 basehttp 19612 7776 "GET /api/patients/patients/ HTTP/1.1" 200 4771
INFO 2025-07-12 18:20:45,254 basehttp 19612 6196 "GET /api/patients/patients/ HTTP/1.1" 200 152
INFO 2025-07-12 18:20:45,286 basehttp 19612 13852 "GET /api/patients/patients/ HTTP/1.1" 200 152
WARNING 2025-07-12 18:20:45,320 log 19612 14108 Not Found: /api/patients/patients/17/
WARNING 2025-07-12 18:20:45,321 basehttp 19612 14108 "GET /api/patients/patients/17/ HTTP/1.1" 404 146
INFO 2025-07-12 18:20:45,380 basehttp 19612 12732 "GET /api/appointments/appointments/ HTTP/1.1" 200 3151
INFO 2025-07-12 18:20:45,419 basehttp 19612 22536 "GET /api/appointments/appointments/ HTTP/1.1" 200 152
INFO 2025-07-12 18:20:45,445 basehttp 19612 18992 "GET /api/appointments/appointments/ HTTP/1.1" 200 152
WARNING 2025-07-12 18:20:45,472 log 19612 5340 Bad Request: /api/appointments/appointments/
WARNING 2025-07-12 18:20:45,473 basehttp 19612 5340 "POST /api/appointments/appointments/ HTTP/1.1" 400 183
INFO 2025-07-12 18:20:45,505 basehttp 19612 17716 "GET /api/patients/medical-records/ HTTP/1.1" 200 152
INFO 2025-07-12 18:20:45,539 basehttp 19612 23080 "GET /api/patients/medical-records/ HTTP/1.1" 200 152
INFO 2025-07-12 18:20:45,563 basehttp 19612 12416 "GET /api/patients/medical-records/ HTTP/1.1" 200 152
WARNING 2025-07-12 18:20:45,595 log 19612 7920 Not Found: /api/billing/bills/
WARNING 2025-07-12 18:20:45,595 basehttp 19612 7920 "GET /api/billing/bills/ HTTP/1.1" 404 13014
WARNING 2025-07-12 18:20:45,626 log 19612 14836 Not Found: /api/billing/bills/
WARNING 2025-07-12 18:20:45,627 basehttp 19612 14836 "GET /api/billing/bills/ HTTP/1.1" 404 13014
WARNING 2025-07-12 18:20:45,654 log 19612 22164 Not Found: /api/billing/bills/
WARNING 2025-07-12 18:20:45,655 basehttp 19612 22164 "GET /api/billing/bills/ HTTP/1.1" 404 13014
INFO 2025-07-12 18:20:45,678 basehttp 19612 620 "GET /api/ai/ HTTP/1.1" 200 595
INFO 2025-07-12 18:20:45,692 basehttp 19612 23508 "GET /api/ai/ HTTP/1.1" 200 595
WARNING 2025-07-12 18:21:04,027 log 19612 4196 Unauthorized: /api/
WARNING 2025-07-12 18:21:04,028 basehttp 19612 4196 "GET /api/ HTTP/1.1" 401 169
INFO 2025-07-12 20:58:33,841 autoreload 20484 17172 Watching for file changes with StatReloader
INFO 2025-07-12 20:58:52,555 multi_agent_system 20484 17172 Initialized agent: Triage Specialist
INFO 2025-07-12 20:58:52,557 multi_agent_system 20484 17172 Initialized agent: Diagnostic Specialist
INFO 2025-07-12 20:58:52,560 multi_agent_system 20484 17172 Initialized agent: Treatment Specialist
INFO 2025-07-12 20:58:52,562 multi_agent_system 20484 17172 Initialized agent: Clinical Pharmacist
INFO 2025-07-12 20:59:25,054 basehttp 20484 18664 "OPTIONS /api/auth/login/ HTTP/1.1" 200 0
WARNING 2025-07-12 20:59:25,122 log 20484 18664 Unauthorized: /api/auth/login/
WARNING 2025-07-12 20:59:25,123 basehttp 20484 18664 "POST /api/auth/login/ HTTP/1.1" 401 289
INFO 2025-07-12 20:59:25,128 basehttp 20484 18664 "OPTIONS /api/auth/token/refresh/ HTTP/1.1" 200 0
WARNING 2025-07-12 20:59:25,138 log 20484 18664 Unauthorized: /api/auth/token/refresh/
WARNING 2025-07-12 20:59:25,138 basehttp 20484 18664 "POST /api/auth/token/refresh/ HTTP/1.1" 401 158
INFO 2025-07-12 21:00:59,773 autoreload 19832 19896 Watching for file changes with StatReloader
WARNING 2025-07-12 21:01:00,978 ai_core 19832 19896 Gemini AI dependencies not installed
WARNING 2025-07-12 21:01:00,978 ai_core 19832 19896 LangGraph dependencies not installed
WARNING 2025-07-12 21:01:00,979 ai_core 19832 19896 ML dependencies not installed
WARNING 2025-07-12 21:01:00,979 ai_core 19832 19896 Gemini AI dependencies not available
WARNING 2025-07-12 21:01:00,979 ai_core 19832 19896 Gemini AI dependencies not available
INFO 2025-07-12 21:01:00,993 multi_agent_system 19832 19896 Initialized agent: Triage Specialist
INFO 2025-07-12 21:01:00,993 multi_agent_system 19832 19896 Initialized agent: Diagnostic Specialist
INFO 2025-07-12 21:01:00,994 multi_agent_system 19832 19896 Initialized agent: Treatment Specialist
INFO 2025-07-12 21:01:00,994 multi_agent_system 19832 19896 Initialized agent: Clinical Pharmacist
INFO 2025-07-12 21:01:32,868 autoreload 14764 1500 Watching for file changes with StatReloader
INFO 2025-07-12 21:01:41,507 multi_agent_system 14764 1500 Initialized agent: Triage Specialist
INFO 2025-07-12 21:01:41,511 multi_agent_system 14764 1500 Initialized agent: Diagnostic Specialist
INFO 2025-07-12 21:01:41,514 multi_agent_system 14764 1500 Initialized agent: Treatment Specialist
INFO 2025-07-12 21:01:41,516 multi_agent_system 14764 1500 Initialized agent: Clinical Pharmacist
INFO 2025-07-12 21:06:45,124 autoreload 5888 11896 Watching for file changes with StatReloader
INFO 2025-07-12 21:06:51,256 multi_agent_system 5888 11896 Initialized agent: Triage Specialist
INFO 2025-07-12 21:06:51,259 multi_agent_system 5888 11896 Initialized agent: Diagnostic Specialist
INFO 2025-07-12 21:06:51,260 multi_agent_system 5888 11896 Initialized agent: Treatment Specialist
INFO 2025-07-12 21:06:51,262 multi_agent_system 5888 11896 Initialized agent: Clinical Pharmacist
INFO 2025-07-12 21:33:49,471 autoreload 15352 16364 Watching for file changes with StatReloader
INFO 2025-07-12 21:33:54,971 multi_agent_system 15352 16364 Initialized agent: Triage Specialist
INFO 2025-07-12 21:33:54,974 multi_agent_system 15352 16364 Initialized agent: Diagnostic Specialist
INFO 2025-07-12 21:33:54,975 multi_agent_system 15352 16364 Initialized agent: Treatment Specialist
INFO 2025-07-12 21:33:54,977 multi_agent_system 15352 16364 Initialized agent: Clinical Pharmacist
INFO 2025-07-12 21:35:18,021 basehttp 19832 12092 "POST /api/auth/login/ HTTP/1.1" 200 804
