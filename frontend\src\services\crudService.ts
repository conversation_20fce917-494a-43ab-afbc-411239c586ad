import { apiService } from '../utils/api';

export interface CRUDField {
  name: string;
  label: string;
  type: 'text' | 'email' | 'password' | 'number' | 'date' | 'select' | 'textarea';
  required?: boolean;
  options?: { value: string; label: string }[];
  placeholder?: string;
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
    message?: string;
  };
}

export interface CRUDConfig {
  endpoint: string;
  title: string;
  fields: CRUDField[];
  displayFields: string[];
  searchFields?: string[];
  idField?: string;
}

class CRUDService {
  async getItems(config: CRUDConfig, page = 1, pageSize = 20, search?: string) {
    let url = `${config.endpoint}?page=${page}&page_size=${pageSize}`;
    
    if (search && config.searchFields) {
      const searchParams = config.searchFields
        .map(field => `${field}__icontains=${encodeURIComponent(search)}`)
        .join('&');
      url += `&${searchParams}`;
    }
    
    return apiService.getPaginated(url);
  }

  async getItem(config: CRUDConfig, id: number | string) {
    return apiService.get(`${config.endpoint}${id}/`);
  }

  async createItem(config: CRUDConfig, data: any) {
    return apiService.post(config.endpoint, data);
  }

  async updateItem(config: CRUDConfig, id: number | string, data: any) {
    return apiService.patch(`${config.endpoint}${id}/`, data);
  }

  async deleteItem(config: CRUDConfig, id: number | string) {
    return apiService.delete(`${config.endpoint}${id}/`);
  }

  validateField(field: CRUDField, value: any): string | null {
    if (field.required && (!value || value.toString().trim() === '')) {
      return `${field.label} is required`;
    }

    if (field.validation) {
      const { min, max, pattern, message } = field.validation;
      
      if (min !== undefined && value && value.toString().length < min) {
        return message || `${field.label} must be at least ${min} characters`;
      }
      
      if (max !== undefined && value && value.toString().length > max) {
        return message || `${field.label} must be no more than ${max} characters`;
      }
      
      if (pattern && value && !new RegExp(pattern).test(value.toString())) {
        return message || `${field.label} format is invalid`;
      }
    }

    return null;
  }

  validateForm(config: CRUDConfig, data: any): Record<string, string> {
    const errors: Record<string, string> = {};
    
    config.fields.forEach(field => {
      const error = this.validateField(field, data[field.name]);
      if (error) {
        errors[field.name] = error;
      }
    });
    
    return errors;
  }

  formatDisplayValue(value: any, field: CRUDField): string {
    if (value === null || value === undefined) {
      return '-';
    }

    switch (field.type) {
      case 'date':
        return new Date(value).toLocaleDateString();
      case 'select':
        const option = field.options?.find(opt => opt.value === value);
        return option ? option.label : value.toString();
      default:
        return value.toString();
    }
  }

  getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : null;
    }, obj);
  }

  setNestedValue(obj: any, path: string, value: any): void {
    const keys = path.split('.');
    const lastKey = keys.pop();
    
    if (!lastKey) return;
    
    const target = keys.reduce((current, key) => {
      if (!current[key]) {
        current[key] = {};
      }
      return current[key];
    }, obj);
    
    target[lastKey] = value;
  }
}

export const crudService = new CRUDService();
