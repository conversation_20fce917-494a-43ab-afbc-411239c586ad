@import './styles/ai-animations.css';
@import './styles/components.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Enhanced Sidebar Styling */
#main-dashboard-sidebar {
  position: relative;
  z-index: 10;
}

/* Prevent sidebar duplication */
#main-dashboard-sidebar + #main-dashboard-sidebar {
  display: none !important;
}

/* Custom Scrollbar for Sidebar */
.scrollbar-thin {
  scrollbar-width: thin;
}

.scrollbar-thumb-gray-300::-webkit-scrollbar-thumb {
  background-color: rgb(209 213 219);
  border-radius: 6px;
}

.scrollbar-thumb-gray-600::-webkit-scrollbar-thumb {
  background-color: rgb(75 85 99);
  border-radius: 6px;
}

.scrollbar-track-transparent::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  border-radius: 6px;
  background-color: rgba(156, 163, 175, 0.5);
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.8);
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
}

/* Sidebar Animation */
@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Enhanced Dark Mode Scrollbar */
.dark .scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: rgba(75, 85, 99, 0.5);
}

.dark .scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background-color: rgba(75, 85, 99, 0.8);
}

@layer base {
  :root {
    /* Enhanced macOS-style light theme */
    --background: 255 255 255;
    --foreground: 28 28 30;
    --card: 255 255 255;
    --card-foreground: 28 28 30;
    --popover: 255 255 255;
    --popover-foreground: 28 28 30;
    --primary: 0 122 255;
    --primary-foreground: 255 255 255;
    --secondary: 248 249 250;
    --secondary-foreground: 28 28 30;
    --muted: 242 243 245;
    --muted-foreground: 99 99 102;
    --accent: 248 249 250;
    --accent-foreground: 28 28 30;
    --destructive: 255 59 48;
    --destructive-foreground: 255 255 255;
    --border: 0 0 0 / 0.1;
    --input: 242 243 245;
    --ring: 0 122 255;
    --radius: 12px;

    /* macOS-specific shadows */
    --shadow-glass: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
    --shadow-elevated: 0 20px 25px -5px rgb(0 0 0 / 0.1);
  }

  .dark {
    /* Enhanced macOS-style dark theme */
    --background: 28 28 30;
    --foreground: 255 255 255;
    --card: 44 44 46;
    --card-foreground: 255 255 255;
    --popover: 44 44 46;
    --popover-foreground: 255 255 255;
    --primary: 10 132 255;
    --primary-foreground: 255 255 255;
    --secondary: 58 58 60;
    --secondary-foreground: 255 255 255;
    --muted: 58 58 60;
    --muted-foreground: 174 174 178;
    --accent: 58 58 60;
    --accent-foreground: 255 255 255;
    --destructive: 255 69 58;
    --destructive-foreground: 255 255 255;
    --border: 255 255 255 / 0.1;
    --input: 58 58 60;
    --ring: 10 132 255;

    /* Dark mode shadows */
    --shadow-glass: 0 8px 32px 0 rgba(0, 0, 0, 0.6);
    --shadow-elevated: 0 20px 25px -5px rgb(0 0 0 / 0.4);
  }
}

@layer base {
  * {
    border-color: hsl(var(--border));
  }

  body {
    @apply bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-indigo-900 text-foreground transition-colors duration-300;
  }

  /* RTL Support */
  [dir="rtl"] {
    direction: rtl;
  }

  [dir="ltr"] {
    direction: ltr;
  }

  /* RTL-specific styles */
  .rtl\:text-right[dir="rtl"] {
    text-align: right;
  }

  .rtl\:text-left[dir="rtl"] {
    text-align: left;
  }

  .rtl\:mr-0[dir="rtl"] {
    margin-right: 0;
  }

  .rtl\:ml-3[dir="rtl"] {
    margin-left: 0.75rem;
  }

  .rtl\:ml-2[dir="rtl"] {
    margin-left: 0.5rem;
  }

  /* Smooth transitions for theme changes */
  *,
  *::before,
  *::after {
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 300ms;
  }

  /* Respect reduced motion preference */
  .reduce-motion *,
  .reduce-motion *::before,
  .reduce-motion *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
      scroll-behavior: auto !important;
    }
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    background: hsl(var(--muted));
  }

  ::-webkit-scrollbar-thumb {
    background: hsl(var(--muted-foreground) / 0.5);
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--muted-foreground) / 0.7);
  }

  /* Focus styles */
  button:focus-visible,
  input:focus-visible,
  select:focus-visible,
  textarea:focus-visible {
    outline: 2px solid hsl(var(--ring));
    outline-offset: 2px;
  }

  /* App root styles */
  #root {
    max-width: 100%;
    margin: 0 auto;
    text-align: left;
  }

  .App {
    min-height: 100vh;
  }
}

/* Additional theme-aware utilities */
@layer utilities {
  .macos-rounded {
    border-radius: 0.5rem;
  }

  .macos-rounded-lg {
    border-radius: 0.75rem;
  }

  .macos-focus {
    @apply focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;
  }

  .gradient-text-enhanced {
    background: linear-gradient(135deg, #06b6d4, #3b82f6, #8b5cf6, #10b981);
    background-size: 400% 400%;
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: gradient-shift 8s ease-in-out infinite;
  }

  @keyframes gradient-shift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
  }

  .text-shadow-xl {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  }
}

/* Dropdown Z-Index Utilities */
@layer utilities {
  .dropdown-overlay {
    z-index: 99998 !important;
  }

  .dropdown-content {
    z-index: 99999 !important;
  }

  /* Ensure dropdowns appear above all content */
  .dropdown-container {
    position: relative;
    z-index: 100000 !important;
  }

  /* Force maximum z-index for critical dropdowns */
  .dropdown-max-z {
    z-index: 2147483647 !important;
  }
}
