@import './styles/ai-animations.css';
@import './styles/components.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Consolidated Sidebar Styles */
#main-dashboard-sidebar {
  position: relative;
  z-index: 10;
}

#main-dashboard-sidebar + #main-dashboard-sidebar {
  display: none !important;
}

/* Sidebar Base Styles */
.sidebar-glass {
  @apply bg-white/90 dark:bg-gray-900/90 backdrop-blur-2xl;
  background: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(248,250,252,0.95) 100%);
  backdrop-filter: blur(20px) saturate(180%);
}

.dark .sidebar-glass {
  background: linear-gradient(135deg, rgba(17,24,39,0.95) 0%, rgba(31,41,55,0.95) 100%);
}

.sidebar-header {
  @apply p-6 border-b border-gray-200/30 dark:border-gray-700/30 bg-gradient-to-r from-blue-50/50 to-indigo-50/50 dark:from-gray-800/50 dark:to-gray-700/50;
}

.sidebar-icon {
  @apply w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center shadow-lg;
}

.sidebar-title {
  @apply text-xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent;
}

.sidebar-user-card {
  @apply bg-white/60 dark:bg-gray-800/60 rounded-lg p-3 backdrop-blur-sm;
}

.sidebar-nav {
  @apply flex-1 overflow-y-auto overflow-x-hidden px-4 py-4;
  scrollbar-width: thin;
}

.sidebar-nav::-webkit-scrollbar {
  width: 6px;
}

.sidebar-nav::-webkit-scrollbar-thumb {
  @apply bg-gray-300/50 dark:bg-gray-600/50 rounded-md;
}

.sidebar-nav::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-300/80 dark:bg-gray-600/80;
}

.sidebar-nav::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-nav-item {
  @apply w-full flex items-center px-4 py-3 text-left rtl:text-right text-sm font-medium transition-all duration-300 rounded-xl group relative overflow-hidden;
  @apply text-gray-700 dark:text-gray-300 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 dark:hover:from-gray-800 dark:hover:to-gray-700;
  @apply hover:text-blue-600 dark:hover:text-blue-400 hover:shadow-md hover:scale-[1.01];
  animation: slideInLeft 0.3s ease-out forwards;
}

.sidebar-nav-item--active {
  @apply bg-gradient-to-r from-blue-500 via-blue-600 to-indigo-600 text-white shadow-lg shadow-blue-500/30 scale-[1.02];
}

.sidebar-nav-indicator {
  @apply absolute left-0 top-0 bottom-0 w-1 bg-white rounded-r-full;
}

.sidebar-nav-icon {
  @apply text-xl transition-all duration-300 group-hover:scale-110 mr-4 rtl:mr-0 rtl:ml-4;
}

.sidebar-nav-label {
  @apply flex-1 rtl:text-right font-medium tracking-wide;
}

.sidebar-bottom {
  @apply border-t border-gray-200/30 dark:border-gray-700/30 p-4 bg-gradient-to-t from-gray-50/80 to-transparent dark:from-gray-800/80 backdrop-blur-xl space-y-3;
}

.sidebar-profile-btn {
  @apply w-full flex items-center px-4 py-3 text-left rtl:text-right text-sm font-medium transition-all duration-300 rounded-xl group;
  @apply text-gray-700 dark:text-gray-300 hover:bg-gradient-to-r hover:from-emerald-50 hover:to-teal-50 dark:hover:from-gray-800 dark:hover:to-gray-700;
  @apply hover:text-emerald-600 dark:hover:text-emerald-400 hover:shadow-md hover:scale-[1.01];
}

.sidebar-profile-btn--active {
  @apply bg-gradient-to-r from-emerald-500 to-teal-600 text-white shadow-lg shadow-emerald-500/25 scale-[1.02];
}

.sidebar-avatar {
  @apply w-11 h-11 bg-gradient-to-br from-emerald-500 to-teal-600 text-white rounded-xl flex items-center justify-center mr-3 rtl:mr-0 rtl:ml-3 shadow-lg group-hover:shadow-xl transition-all duration-300;
}

.sidebar-settings-btn {
  @apply w-full flex items-center px-4 py-3 text-left rtl:text-right text-sm font-medium transition-all duration-300 rounded-xl group;
  @apply text-gray-700 dark:text-gray-300 hover:bg-gradient-to-r hover:from-purple-50 hover:to-violet-50 dark:hover:from-gray-800 dark:hover:to-gray-700;
  @apply hover:text-purple-600 dark:hover:text-purple-400 hover:shadow-md hover:scale-[1.01];
}

.sidebar-settings-btn--active {
  @apply bg-gradient-to-r from-purple-500 to-violet-600 text-white shadow-lg shadow-purple-500/25 scale-[1.02];
}

.sidebar-settings-icon {
  @apply mr-3 rtl:mr-0 rtl:ml-3 text-lg transition-transform duration-300 group-hover:rotate-90;
}

.sidebar-logout-btn {
  @apply w-full flex items-center justify-center px-4 py-3 text-sm font-medium transition-all duration-300 rounded-xl group;
  @apply bg-gradient-to-r from-red-500 to-rose-600 text-white shadow-lg shadow-red-500/25 hover:shadow-xl hover:shadow-red-500/30 hover:scale-[1.02];
}

.sidebar-logout-icon {
  @apply mr-2 rtl:mr-0 rtl:ml-2 text-lg transition-transform duration-300 group-hover:translate-x-1;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@layer base {
  :root {
    /* Enhanced macOS-style light theme */
    --background: 255 255 255;
    --foreground: 28 28 30;
    --card: 255 255 255;
    --card-foreground: 28 28 30;
    --popover: 255 255 255;
    --popover-foreground: 28 28 30;
    --primary: 0 122 255;
    --primary-foreground: 255 255 255;
    --secondary: 248 249 250;
    --secondary-foreground: 28 28 30;
    --muted: 242 243 245;
    --muted-foreground: 99 99 102;
    --accent: 248 249 250;
    --accent-foreground: 28 28 30;
    --destructive: 255 59 48;
    --destructive-foreground: 255 255 255;
    --border: 0 0 0 / 0.1;
    --input: 242 243 245;
    --ring: 0 122 255;
    --radius: 12px;

    /* macOS-specific shadows */
    --shadow-glass: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
    --shadow-elevated: 0 20px 25px -5px rgb(0 0 0 / 0.1);
  }

  .dark {
    /* Enhanced macOS-style dark theme */
    --background: 28 28 30;
    --foreground: 255 255 255;
    --card: 44 44 46;
    --card-foreground: 255 255 255;
    --popover: 44 44 46;
    --popover-foreground: 255 255 255;
    --primary: 10 132 255;
    --primary-foreground: 255 255 255;
    --secondary: 58 58 60;
    --secondary-foreground: 255 255 255;
    --muted: 58 58 60;
    --muted-foreground: 174 174 178;
    --accent: 58 58 60;
    --accent-foreground: 255 255 255;
    --destructive: 255 69 58;
    --destructive-foreground: 255 255 255;
    --border: 255 255 255 / 0.1;
    --input: 58 58 60;
    --ring: 10 132 255;

    /* Dark mode shadows */
    --shadow-glass: 0 8px 32px 0 rgba(0, 0, 0, 0.6);
    --shadow-elevated: 0 20px 25px -5px rgb(0 0 0 / 0.4);
  }
}

@layer base {
  * {
    border-color: hsl(var(--border));
  }

  body {
    @apply bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-indigo-900 text-foreground transition-colors duration-300;
  }

  /* RTL Support */
  [dir="rtl"] {
    direction: rtl;
  }

  [dir="ltr"] {
    direction: ltr;
  }

  /* RTL-specific styles */
  .rtl\:text-right[dir="rtl"] {
    text-align: right;
  }

  .rtl\:text-left[dir="rtl"] {
    text-align: left;
  }

  .rtl\:mr-0[dir="rtl"] {
    margin-right: 0;
  }

  .rtl\:ml-3[dir="rtl"] {
    margin-left: 0.75rem;
  }

  .rtl\:ml-2[dir="rtl"] {
    margin-left: 0.5rem;
  }

  /* Smooth transitions for theme changes */
  *,
  *::before,
  *::after {
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 300ms;
  }

  /* Respect reduced motion preference */
  .reduce-motion *,
  .reduce-motion *::before,
  .reduce-motion *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
      scroll-behavior: auto !important;
    }
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    background: hsl(var(--muted));
  }

  ::-webkit-scrollbar-thumb {
    background: hsl(var(--muted-foreground) / 0.5);
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--muted-foreground) / 0.7);
  }

  /* Focus styles */
  button:focus-visible,
  input:focus-visible,
  select:focus-visible,
  textarea:focus-visible {
    outline: 2px solid hsl(var(--ring));
    outline-offset: 2px;
  }

  /* App root styles */
  #root {
    max-width: 100%;
    margin: 0 auto;
    text-align: left;
  }

  .App {
    min-height: 100vh;
  }
}

/* Additional theme-aware utilities */
@layer utilities {
  .macos-rounded {
    border-radius: 0.5rem;
  }

  .macos-rounded-lg {
    border-radius: 0.75rem;
  }

  .macos-focus {
    @apply focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;
  }

  .gradient-text-enhanced {
    background: linear-gradient(135deg, #06b6d4, #3b82f6, #8b5cf6, #10b981);
    background-size: 400% 400%;
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: gradient-shift 8s ease-in-out infinite;
  }

  @keyframes gradient-shift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
  }

  .text-shadow-xl {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  }
}

/* Dropdown Z-Index Utilities */
@layer utilities {
  .dropdown-overlay {
    z-index: 99998 !important;
  }

  .dropdown-content {
    z-index: 99999 !important;
  }

  /* Ensure dropdowns appear above all content */
  .dropdown-container {
    position: relative;
    z-index: 100000 !important;
  }

  /* Force maximum z-index for critical dropdowns */
  .dropdown-max-z {
    z-index: 2147483647 !important;
  }
}
