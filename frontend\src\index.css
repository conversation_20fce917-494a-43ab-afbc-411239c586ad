@tailwind base;
@tailwind components;
@tailwind utilities;

/* Basic styles for testing */
.glass {
  backdrop-filter: blur(12px);
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark .glass {
  background: rgba(17, 24, 39, 0.8);
  border: 1px solid rgba(75, 85, 99, 0.2);
}

/* Sidebar styles */
#main-dashboard-sidebar {
  position: relative;
  z-index: 10;
}

.sidebar {
  width: 18rem;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  padding: 1.5rem;
  border-bottom: 1px solid rgba(156, 163, 175, 0.3);
}

.sidebar-nav {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
}

.sidebar-nav-item {
  width: 100%;
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  margin-bottom: 0.5rem;
  border-radius: 0.75rem;
  transition: all 0.3s ease;
  color: rgb(55, 65, 81);
}

.sidebar-nav-item:hover {
  background: rgba(59, 130, 246, 0.1);
  color: rgb(59, 130, 246);
}

.sidebar-nav-item--active {
  background: linear-gradient(135deg, rgb(59, 130, 246), rgb(99, 102, 241));
  color: white;
}

/* Dark mode */
.dark .sidebar-nav-item {
  color: rgb(209, 213, 219);
}

.dark .sidebar-nav-item:hover {
  background: rgba(75, 85, 99, 0.5);
  color: rgb(147, 197, 253);
}
