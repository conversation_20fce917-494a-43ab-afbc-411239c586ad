@import './styles/ai-animations.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* =================================
   UNIFIED STYLE SYSTEM - NO DUPLICATES
   ================================= */

/* Prevent sidebar duplication */
#main-dashboard-sidebar {
  position: relative;
  z-index: 10;
}

#main-dashboard-sidebar + #main-dashboard-sidebar {
  display: none !important;
}

/* =================================
   UNIFIED GLASSMORPHISM SYSTEM
   ================================= */

.glass {
  @apply backdrop-blur-xl bg-white/80 dark:bg-gray-900/80 border border-white/20 dark:border-gray-700/20 shadow-xl;
}

.glass-light {
  @apply backdrop-blur-lg bg-white/60 dark:bg-gray-900/60 border border-white/10 dark:border-gray-700/10 shadow-lg;
}

.glass-medium {
  @apply backdrop-blur-xl bg-white/70 dark:bg-gray-900/70 border border-white/15 dark:border-gray-700/15 shadow-xl;
}

.glass-heavy {
  @apply backdrop-blur-2xl bg-white/90 dark:bg-gray-900/90 border border-white/30 dark:border-gray-700/30 shadow-2xl;
}

/* Legacy aliases for backward compatibility */
.macos-card {
  @apply glass rounded-xl p-6;
}

.macos-button {
  @apply glass text-gray-700 dark:text-gray-300 hover:scale-[1.02] focus:ring-blue-500 px-4 py-2 rounded-lg font-medium transition-all duration-200;
}

.macos-input {
  @apply glass rounded-lg px-3 py-2 focus:glass-medium transition-all duration-200;
}

.macos-accent-bg {
  @apply bg-blue-500/90 backdrop-blur-md;
}

.macos-text-primary {
  @apply text-gray-900 dark:text-white;
}

.macos-text-secondary {
  @apply text-gray-600 dark:text-gray-300;
}

.macos-text-tertiary {
  @apply text-gray-500 dark:text-gray-400;
}

.macos-accent-text {
  @apply text-blue-600 dark:text-blue-400;
}

.macos-transition {
  @apply transition-all duration-200 ease-in-out;
}

.macos-focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:ring-offset-2;
}

/* =================================
   UNIFIED BUTTON SYSTEM
   ================================= */

.btn {
  @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-xl transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2;
}

.btn-primary {
  background: linear-gradient(135deg, rgb(var(--color-blue)), rgb(var(--color-indigo)));
  @apply text-white shadow-lg hover:shadow-xl hover:scale-[1.02] focus:ring-blue-500;
}

.btn-secondary {
  background: linear-gradient(135deg, rgb(var(--color-purple)), rgb(var(--color-indigo)));
  @apply text-white shadow-lg hover:shadow-xl hover:scale-[1.02] focus:ring-purple-500;
}

.btn-success {
  background: linear-gradient(135deg, rgb(var(--color-emerald)), rgb(var(--color-teal)));
  @apply text-white shadow-lg hover:shadow-xl hover:scale-[1.02] focus:ring-emerald-500;
}

.btn-danger {
  background: linear-gradient(135deg, rgb(var(--color-red)), rgb(var(--color-orange)));
  @apply text-white shadow-lg hover:shadow-xl hover:scale-[1.02] focus:ring-red-500;
}

.btn-glass {
  @apply glass text-gray-700 dark:text-gray-300 hover:scale-[1.02] focus:ring-blue-500;
}

/* =================================
   UNIFIED CARD SYSTEM
   ================================= */

.card {
  @apply glass rounded-xl p-6;
}

.card-elevated {
  @apply glass-medium rounded-xl p-6;
}

.card-heavy {
  @apply glass-heavy rounded-xl p-6;
}

/* =================================
   UNIFIED STATUS SYSTEM
   ================================= */

.status-success {
  @apply bg-emerald-100/80 text-emerald-800 dark:bg-emerald-900/20 dark:text-emerald-400 border border-emerald-200/50 dark:border-emerald-700/50 px-2 py-1 rounded-md text-xs font-medium;
}

.status-warning {
  @apply bg-yellow-100/80 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400 border border-yellow-200/50 dark:border-yellow-700/50 px-2 py-1 rounded-md text-xs font-medium;
}

.status-error {
  @apply bg-red-100/80 text-red-800 dark:bg-red-900/20 dark:text-red-400 border border-red-200/50 dark:border-red-700/50 px-2 py-1 rounded-md text-xs font-medium;
}

.status-info {
  @apply bg-blue-100/80 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400 border border-blue-200/50 dark:border-blue-700/50 px-2 py-1 rounded-md text-xs font-medium;
}

.status-neutral {
  @apply bg-gray-100/80 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400 border border-gray-200/50 dark:border-gray-700/50 px-2 py-1 rounded-md text-xs font-medium;
}

/* =================================
   UNIFIED GRADIENT SYSTEM
   ================================= */

.gradient-primary {
  background: linear-gradient(135deg, rgb(var(--color-blue)), rgb(var(--color-indigo)));
}

.gradient-secondary {
  background: linear-gradient(135deg, rgb(var(--color-purple)), rgb(var(--color-indigo)));
}

.gradient-success {
  background: linear-gradient(135deg, rgb(var(--color-emerald)), rgb(var(--color-teal)));
}

.gradient-danger {
  background: linear-gradient(135deg, rgb(var(--color-red)), rgb(var(--color-orange)));
}

.gradient-warning {
  background: linear-gradient(135deg, rgb(var(--color-yellow)), rgb(var(--color-orange)));
}

/* =================================
   UNIFIED SIDEBAR SYSTEM
   ================================= */

.sidebar {
  @apply glass-heavy w-72 flex-shrink-0 flex flex-col;
}

.sidebar-header {
  @apply p-6 border-b border-gray-200/30 dark:border-gray-700/30;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(99, 102, 241, 0.1));
}

.sidebar-icon {
  @apply w-10 h-10 rounded-xl flex items-center justify-center shadow-lg gradient-primary;
}

.sidebar-title {
  @apply text-xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent;
}

.sidebar-user-card {
  @apply glass rounded-lg p-3;
}

.sidebar-nav {
  @apply flex-1 overflow-y-auto overflow-x-hidden px-4 py-4;
  scrollbar-width: thin;
}

.sidebar-nav::-webkit-scrollbar {
  width: 6px;
}

.sidebar-nav::-webkit-scrollbar-thumb {
  @apply bg-gray-300/50 dark:bg-gray-600/50 rounded-md;
}

.sidebar-nav::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-300/80 dark:bg-gray-600/80;
}

.sidebar-nav::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-nav-item {
  @apply btn text-left rtl:text-right relative overflow-hidden;
  @apply text-gray-700 dark:text-gray-300 hover:glass hover:text-blue-600 dark:hover:text-blue-400 hover:scale-[1.01];
  animation: slideInLeft 0.3s ease-out forwards;
}

.sidebar-nav-item--active {
  @apply gradient-primary text-white shadow-lg shadow-blue-500/30 scale-[1.02];
}

.sidebar-nav-indicator {
  @apply absolute left-0 top-0 bottom-0 w-1 bg-white rounded-r-full;
}

.sidebar-nav-icon {
  @apply text-xl transition-all duration-300 mr-4 rtl:mr-0 rtl:ml-4;
}

.group:hover .sidebar-nav-icon {
  @apply scale-110;
}

.sidebar-nav-label {
  @apply flex-1 rtl:text-right font-medium tracking-wide;
}

.sidebar-bottom {
  @apply border-t border-gray-200/30 dark:border-gray-700/30 p-4 space-y-3;
  background: linear-gradient(to top, rgba(249, 250, 251, 0.8), transparent);
}

.dark .sidebar-bottom {
  background: linear-gradient(to top, rgba(31, 41, 55, 0.8), transparent);
}

.sidebar-profile-btn {
  @apply btn text-left rtl:text-right;
  @apply text-gray-700 dark:text-gray-300 hover:glass hover:text-emerald-600 dark:hover:text-emerald-400;
}

.sidebar-profile-btn--active {
  @apply gradient-success text-white shadow-lg shadow-emerald-500/25 scale-[1.02];
}

.sidebar-avatar {
  @apply w-11 h-11 gradient-success text-white rounded-xl flex items-center justify-center mr-3 rtl:mr-0 rtl:ml-3 shadow-lg transition-all duration-300;
}

.group:hover .sidebar-avatar {
  @apply shadow-xl;
}

.sidebar-settings-btn {
  @apply btn text-left rtl:text-right;
  @apply text-gray-700 dark:text-gray-300 hover:glass hover:text-purple-600 dark:hover:text-purple-400;
}

.sidebar-settings-btn--active {
  @apply gradient-secondary text-white shadow-lg shadow-purple-500/25 scale-[1.02];
}

.sidebar-settings-icon {
  @apply mr-3 rtl:mr-0 rtl:ml-3 text-lg transition-transform duration-300;
}

.group:hover .sidebar-settings-icon {
  @apply rotate-90;
}

.sidebar-logout-btn {
  @apply btn-danger;
}

.sidebar-logout-icon {
  @apply mr-2 rtl:mr-0 rtl:ml-2 text-lg transition-transform duration-300 group-hover:translate-x-1;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@layer base {
  :root {
    /* Enhanced macOS-style light theme */
    --background: 255 255 255;
    --foreground: 28 28 30;
    --card: 255 255 255;
    --card-foreground: 28 28 30;
    --popover: 255 255 255;
    --popover-foreground: 28 28 30;
    --primary: 0 122 255;
    --primary-foreground: 255 255 255;
    --secondary: 248 249 250;
    --secondary-foreground: 28 28 30;
    --muted: 242 243 245;
    --muted-foreground: 99 99 102;
    --accent: 248 249 250;
    --accent-foreground: 28 28 30;
    --destructive: 255 59 48;
    --destructive-foreground: 255 255 255;
    --border: 0 0 0 / 0.1;
    --input: 242 243 245;
    --ring: 0 122 255;
    --radius: 12px;

    /* Unified Color System */
    --color-blue: 59 130 246;
    --color-indigo: 99 102 241;
    --color-purple: 147 51 234;
    --color-emerald: 16 185 129;
    --color-teal: 20 184 166;
    --color-red: 239 68 68;
    --color-orange: 249 115 22;
    --color-yellow: 245 158 11;
    --color-green: 34 197 94;

    /* Glassmorphism System */
    --glass-light: rgba(255, 255, 255, 0.8);
    --glass-medium: rgba(255, 255, 255, 0.6);
    --glass-heavy: rgba(255, 255, 255, 0.4);
    --glass-border-light: rgba(255, 255, 255, 0.2);
    --glass-border-medium: rgba(255, 255, 255, 0.3);
    --glass-border-heavy: rgba(255, 255, 255, 0.4);

    /* Shadow System */
    --shadow-glass: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
    --shadow-elevated: 0 20px 25px -5px rgb(0 0 0 / 0.1);
    --shadow-soft: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    --shadow-medium: 0 10px 15px -3px rgb(0 0 0 / 0.1);
    --shadow-large: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  }

  .dark {
    /* Enhanced macOS-style dark theme */
    --background: 28 28 30;
    --foreground: 255 255 255;
    --card: 44 44 46;
    --card-foreground: 255 255 255;
    --popover: 44 44 46;
    --popover-foreground: 255 255 255;
    --primary: 10 132 255;
    --primary-foreground: 255 255 255;
    --secondary: 58 58 60;
    --secondary-foreground: 255 255 255;
    --muted: 58 58 60;
    --muted-foreground: 174 174 178;

    /* Dark Mode Glassmorphism */
    --glass-light: rgba(17, 24, 39, 0.8);
    --glass-medium: rgba(17, 24, 39, 0.6);
    --glass-heavy: rgba(17, 24, 39, 0.4);
    --glass-border-light: rgba(75, 85, 99, 0.2);
    --glass-border-medium: rgba(75, 85, 99, 0.3);
    --glass-border-heavy: rgba(75, 85, 99, 0.4);
    --accent: 58 58 60;
    --accent-foreground: 255 255 255;
    --destructive: 255 69 58;
    --destructive-foreground: 255 255 255;
    --border: 255 255 255 / 0.1;
    --input: 58 58 60;
    --ring: 10 132 255;

    /* Dark mode shadows */
    --shadow-glass: 0 8px 32px 0 rgba(0, 0, 0, 0.6);
    --shadow-elevated: 0 20px 25px -5px rgb(0 0 0 / 0.4);
  }
}

@layer base {
  * {
    border-color: hsl(var(--border));
  }

  body {
    @apply bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-indigo-900 text-foreground transition-colors duration-300;
  }

  /* RTL Support */
  [dir="rtl"] {
    direction: rtl;
  }

  [dir="ltr"] {
    direction: ltr;
  }

  /* RTL-specific styles */
  .rtl\:text-right[dir="rtl"] {
    text-align: right;
  }

  .rtl\:text-left[dir="rtl"] {
    text-align: left;
  }

  .rtl\:mr-0[dir="rtl"] {
    margin-right: 0;
  }

  .rtl\:ml-3[dir="rtl"] {
    margin-left: 0.75rem;
  }

  .rtl\:ml-2[dir="rtl"] {
    margin-left: 0.5rem;
  }

  /* Smooth transitions for theme changes */
  *,
  *::before,
  *::after {
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 300ms;
  }

  /* Respect reduced motion preference */
  .reduce-motion *,
  .reduce-motion *::before,
  .reduce-motion *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  @media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
      scroll-behavior: auto !important;
    }
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    background: hsl(var(--muted));
  }

  ::-webkit-scrollbar-thumb {
    background: hsl(var(--muted-foreground) / 0.5);
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--muted-foreground) / 0.7);
  }

  /* Focus styles */
  button:focus-visible,
  input:focus-visible,
  select:focus-visible,
  textarea:focus-visible {
    outline: 2px solid hsl(var(--ring));
    outline-offset: 2px;
  }

  /* App root styles */
  #root {
    max-width: 100%;
    margin: 0 auto;
    text-align: left;
  }

  .App {
    min-height: 100vh;
  }
}

/* Additional theme-aware utilities */
@layer utilities {
  .macos-rounded {
    border-radius: 0.5rem;
  }

  .macos-rounded-lg {
    border-radius: 0.75rem;
  }

  .macos-focus {
    @apply focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2;
  }

  .gradient-text-enhanced {
    background: linear-gradient(135deg, #06b6d4, #3b82f6, #8b5cf6, #10b981);
    background-size: 400% 400%;
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: gradient-shift 8s ease-in-out infinite;
  }

  @keyframes gradient-shift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
  }

  .text-shadow-xl {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  }
}

/* Dropdown Z-Index Utilities */
@layer utilities {
  .dropdown-overlay {
    z-index: 99998 !important;
  }

  .dropdown-content {
    z-index: 99999 !important;
  }

  /* Ensure dropdowns appear above all content */
  .dropdown-container {
    position: relative;
    z-index: 100000 !important;
  }

  /* Force maximum z-index for critical dropdowns */
  .dropdown-max-z {
    z-index: 2147483647 !important;
  }
}
