/**
 * Application Constants
 * Centralized constants for the HMS application
 */

// API Configuration
export const API_CONFIG = {
  BASE_URL: import.meta.env.VITE_API_URL || 'http://localhost:8000/api',
  TIMEOUT: 30000,
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000,
} as const;

// Authentication
export const AUTH_CONFIG = {
  TOKEN_KEY: 'token',
  REFRESH_TOKEN_KEY: 'refreshToken',
  USER_KEY: 'user',
  TOKEN_EXPIRY_BUFFER: 5 * 60 * 1000, // 5 minutes in milliseconds
  SESSION_TIMEOUT: 24 * 60 * 60 * 1000, // 24 hours in milliseconds
} as const;

// User Roles
export const USER_ROLES = {
  ADMIN: 'admin',
  DOCTOR: 'doctor',
  NURSE: 'nurse',
  RECEPTIONIST: 'receptionist',
  PATIENT: 'patient',
  PHARMACIST: 'pharmacist',
  LAB_TECHNICIAN: 'lab_technician',
} as const;

export type UserRole = typeof USER_ROLES[keyof typeof USER_ROLES];

// Appointment Status
export const APPOINTMENT_STATUS = {
  SCHEDULED: 'scheduled',
  CONFIRMED: 'confirmed',
  IN_PROGRESS: 'in_progress',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled',
  NO_SHOW: 'no_show',
  RESCHEDULED: 'rescheduled',
} as const;

export type AppointmentStatus = typeof APPOINTMENT_STATUS[keyof typeof APPOINTMENT_STATUS];

// Patient Status
export const PATIENT_STATUS = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  DISCHARGED: 'discharged',
  ADMITTED: 'admitted',
  EMERGENCY: 'emergency',
} as const;

export type PatientStatus = typeof PATIENT_STATUS[keyof typeof PATIENT_STATUS];

// Medical Record Types
export const MEDICAL_RECORD_TYPES = {
  CONSULTATION: 'consultation',
  DIAGNOSIS: 'diagnosis',
  TREATMENT: 'treatment',
  PRESCRIPTION: 'prescription',
  LAB_RESULT: 'lab_result',
  IMAGING: 'imaging',
  SURGERY: 'surgery',
  DISCHARGE_SUMMARY: 'discharge_summary',
} as const;

export type MedicalRecordType = typeof MEDICAL_RECORD_TYPES[keyof typeof MEDICAL_RECORD_TYPES];

// Priority Levels
export const PRIORITY_LEVELS = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  URGENT: 'urgent',
  CRITICAL: 'critical',
} as const;

export type PriorityLevel = typeof PRIORITY_LEVELS[keyof typeof PRIORITY_LEVELS];

// Emergency Levels
export const EMERGENCY_LEVELS = {
  LEVEL_1: 'level_1', // Resuscitation
  LEVEL_2: 'level_2', // Emergent
  LEVEL_3: 'level_3', // Urgent
  LEVEL_4: 'level_4', // Less Urgent
  LEVEL_5: 'level_5', // Non-Urgent
} as const;

export type EmergencyLevel = typeof EMERGENCY_LEVELS[keyof typeof EMERGENCY_LEVELS];

// Payment Status
export const PAYMENT_STATUS = {
  PENDING: 'pending',
  PAID: 'paid',
  PARTIAL: 'partial',
  OVERDUE: 'overdue',
  CANCELLED: 'cancelled',
  REFUNDED: 'refunded',
} as const;

export type PaymentStatus = typeof PAYMENT_STATUS[keyof typeof PAYMENT_STATUS];

// Insurance Status
export const INSURANCE_STATUS = {
  ACTIVE: 'active',
  INACTIVE: 'inactive',
  EXPIRED: 'expired',
  PENDING: 'pending',
  DENIED: 'denied',
} as const;

export type InsuranceStatus = typeof INSURANCE_STATUS[keyof typeof INSURANCE_STATUS];

// Notification Types
export const NOTIFICATION_TYPES = {
  INFO: 'info',
  SUCCESS: 'success',
  WARNING: 'warning',
  ERROR: 'error',
  APPOINTMENT_REMINDER: 'appointment_reminder',
  MEDICATION_REMINDER: 'medication_reminder',
  LAB_RESULT: 'lab_result',
  EMERGENCY_ALERT: 'emergency_alert',
} as const;

export type NotificationType = typeof NOTIFICATION_TYPES[keyof typeof NOTIFICATION_TYPES];

// File Types
export const ALLOWED_FILE_TYPES = {
  IMAGES: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  DOCUMENTS: ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
  SPREADSHEETS: ['application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'],
  MEDICAL: ['application/dicom', 'image/dicom'],
} as const;

// File Size Limits (in bytes)
export const FILE_SIZE_LIMITS = {
  AVATAR: 2 * 1024 * 1024, // 2MB
  DOCUMENT: 10 * 1024 * 1024, // 10MB
  MEDICAL_IMAGE: 50 * 1024 * 1024, // 50MB
  BULK_UPLOAD: 100 * 1024 * 1024, // 100MB
} as const;

// Pagination
export const PAGINATION = {
  DEFAULT_PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 100,
  PAGE_SIZE_OPTIONS: [10, 20, 50, 100],
} as const;

// Date Formats
export const DATE_FORMATS = {
  DISPLAY: 'MMM DD, YYYY',
  INPUT: 'YYYY-MM-DD',
  DATETIME: 'MMM DD, YYYY HH:mm',
  TIME: 'HH:mm',
  TIME_12H: 'hh:mm A',
} as const;

// Time Slots
export const TIME_SLOTS = {
  MORNING: { start: '08:00', end: '12:00' },
  AFTERNOON: { start: '12:00', end: '17:00' },
  EVENING: { start: '17:00', end: '21:00' },
  NIGHT: { start: '21:00', end: '08:00' },
} as const;

// Working Hours
export const WORKING_HOURS = {
  START: '08:00',
  END: '18:00',
  LUNCH_START: '12:00',
  LUNCH_END: '13:00',
  APPOINTMENT_DURATION: 30, // minutes
} as const;

// Theme Configuration
export const THEME_CONFIG = {
  COLORS: {
    PRIMARY: '#3b82f6',
    SECONDARY: '#6366f1',
    SUCCESS: '#10b981',
    WARNING: '#f59e0b',
    ERROR: '#ef4444',
    INFO: '#06b6d4',
  },
  GLASSMORPHISM: {
    LIGHT: 'rgba(255, 255, 255, 0.8)',
    MEDIUM: 'rgba(255, 255, 255, 0.6)',
    HEAVY: 'rgba(255, 255, 255, 0.4)',
  },
  BREAKPOINTS: {
    SM: '640px',
    MD: '768px',
    LG: '1024px',
    XL: '1280px',
    '2XL': '1536px',
  },
} as const;

// Validation Rules
export const VALIDATION_RULES = {
  PASSWORD: {
    MIN_LENGTH: 8,
    REQUIRE_UPPERCASE: true,
    REQUIRE_LOWERCASE: true,
    REQUIRE_NUMBERS: true,
    REQUIRE_SPECIAL_CHARS: true,
  },
  PHONE: {
    MIN_LENGTH: 10,
    MAX_LENGTH: 15,
  },
  EMAIL: {
    PATTERN: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  },
  NAME: {
    MIN_LENGTH: 2,
    MAX_LENGTH: 50,
  },
} as const;

// Error Messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network error. Please check your connection.',
  UNAUTHORIZED: 'You are not authorized to perform this action.',
  FORBIDDEN: 'Access denied.',
  NOT_FOUND: 'The requested resource was not found.',
  SERVER_ERROR: 'Internal server error. Please try again later.',
  VALIDATION_ERROR: 'Please check your input and try again.',
  SESSION_EXPIRED: 'Your session has expired. Please log in again.',
  UNKNOWN_ERROR: 'An unexpected error occurred.',
} as const;

// Success Messages - Now using translation keys
export const SUCCESS_MESSAGE_KEYS = {
  SAVE_SUCCESS: 'messages.saveSuccess',
  DELETE_SUCCESS: 'messages.deleteSuccess',
  CREATE_SUCCESS: 'messages.createSuccess',
  UPDATE_SUCCESS: 'messages.updateSuccess',
  LOGIN_SUCCESS: 'auth.loginSuccess',
  LOGOUT_SUCCESS: 'auth.logoutSuccess',
  PASSWORD_RESET: 'auth.passwordResetSent',
  EMAIL_VERIFIED: 'auth.emailVerified',
} as const;

// Legacy constants - deprecated, use translation keys instead
export const SUCCESS_MESSAGES = {
  SAVE_SUCCESS: 'Changes saved successfully.',
  DELETE_SUCCESS: 'Item deleted successfully.',
  CREATE_SUCCESS: 'Item created successfully.',
  UPDATE_SUCCESS: 'Item updated successfully.',
  LOGIN_SUCCESS: 'Login successful.',
  LOGOUT_SUCCESS: 'Logout successful.',
  PASSWORD_RESET: 'Password reset email sent.',
  EMAIL_VERIFIED: 'Email verified successfully.',
} as const;

// Local Storage Keys
export const STORAGE_KEYS = {
  THEME: 'hms_theme',
  LANGUAGE: 'hms_language',
  USER_PREFERENCES: 'hms_user_preferences',
  DASHBOARD_LAYOUT: 'hms_dashboard_layout',
  SIDEBAR_COLLAPSED: 'hms_sidebar_collapsed',
  RECENT_SEARCHES: 'hms_recent_searches',
} as const;

// Feature Flags
export const FEATURE_FLAGS = {
  AI_SERVICES: import.meta.env.VITE_ENABLE_AI === 'true',
  DARK_MODE: true,
  MULTI_LANGUAGE: true,
  NOTIFICATIONS: true,
  REAL_TIME_UPDATES: import.meta.env.VITE_ENABLE_WEBSOCKETS === 'true',
  ANALYTICS: import.meta.env.VITE_ENABLE_ANALYTICS === 'true',
} as const;

// AI Service Configuration
export const AI_CONFIG = {
  MAX_CONVERSATION_LENGTH: 50,
  RESPONSE_TIMEOUT: 30000,
  SUPPORTED_LANGUAGES: ['en', 'ar'],
  CONFIDENCE_THRESHOLD: 0.7,
} as const;

// Chart Configuration
export const CHART_CONFIG = {
  COLORS: [
    '#3b82f6', '#10b981', '#f59e0b', '#ef4444',
    '#8b5cf6', '#06b6d4', '#84cc16', '#f97316',
  ],
  ANIMATION_DURATION: 300,
  RESPONSIVE_BREAKPOINT: 768,
} as const;

// Export Status
export const EXPORT_STATUS = {
  PENDING: 'pending',
  PROCESSING: 'processing',
  COMPLETED: 'completed',
  FAILED: 'failed',
} as const;

export type ExportStatus = typeof EXPORT_STATUS[keyof typeof EXPORT_STATUS];

// Export Formats
export const EXPORT_FORMATS = {
  PDF: 'pdf',
  EXCEL: 'xlsx',
  CSV: 'csv',
  JSON: 'json',
} as const;

export type ExportFormat = typeof EXPORT_FORMATS[keyof typeof EXPORT_FORMATS];

// Regular Expressions
export const REGEX_PATTERNS = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PHONE: /^\+?[\d\s\-\(\)]+$/,
  PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
  PATIENT_ID: /^P\d{6}$/,
  APPOINTMENT_ID: /^APT-\d{8}$/,
  MEDICAL_RECORD_NUMBER: /^MRN-\d{6}$/,
  POSTAL_CODE: /^\d{5}(-\d{4})?$/,
  SSN: /^\d{3}-\d{2}-\d{4}$/,
} as const;

// HTTP Status Codes
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  INTERNAL_SERVER_ERROR: 500,
  SERVICE_UNAVAILABLE: 503,
} as const;

// WebSocket Events
export const WEBSOCKET_EVENTS = {
  CONNECT: 'connect',
  DISCONNECT: 'disconnect',
  APPOINTMENT_UPDATE: 'appointment_update',
  PATIENT_UPDATE: 'patient_update',
  EMERGENCY_ALERT: 'emergency_alert',
  NOTIFICATION: 'notification',
  SYSTEM_MESSAGE: 'system_message',
} as const;

// Cache Keys
export const CACHE_KEYS = {
  USER_PROFILE: 'user_profile',
  DASHBOARD_DATA: 'dashboard_data',
  APPOINTMENTS: 'appointments',
  PATIENTS: 'patients',
  NOTIFICATIONS: 'notifications',
} as const;

// Cache TTL (Time To Live) in milliseconds
export const CACHE_TTL = {
  SHORT: 5 * 60 * 1000, // 5 minutes
  MEDIUM: 30 * 60 * 1000, // 30 minutes
  LONG: 2 * 60 * 60 * 1000, // 2 hours
  VERY_LONG: 24 * 60 * 60 * 1000, // 24 hours
} as const;
